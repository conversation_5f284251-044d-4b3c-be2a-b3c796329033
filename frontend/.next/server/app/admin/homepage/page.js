(()=>{var a={};a.id=607,a.ids=[607],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5899:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/homepage/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/homepage/page.tsx","default")},8819:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12298:(a,b,c)=>{Promise.resolve().then(c.bind(c,97141))},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69282:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92159:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["homepage",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,5899)),"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/homepage/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,99111)),"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/homepage/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/homepage/page",pathname:"/admin/homepage",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/homepage/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},94146:(a,b,c)=>{Promise.resolve().then(c.bind(c,5899))},97141:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(60687),e=c(43210),f=c(69282),g=c(13861),h=c(8819);function i(){let[a,b]=(0,e.useState)({header:[],hero:[],what_we_do:[],why_choose_us:[],how_it_works:[],our_expertise:[],testimonials:[],partners:[],final_cta:[],footer:[]}),[c,i]=(0,e.useState)({}),[j,k]=(0,e.useState)(!0),[l,m]=(0,e.useState)(!1),[n,o]=(0,e.useState)("hero"),p=async()=>{try{let a=localStorage.getItem("adminToken"),c=await fetch("http://localhost:5001/api/content",{headers:{Authorization:`Bearer ${a}`}}),d={};c.ok&&(d=await c.json());let e={header:[{section:"header",key:"company_name",value:"RevAdOps",type:"text"},{section:"header",key:"logo",value:"/revadops-logo.png",type:"text"},{section:"header",key:"logo_alt",value:"RevAdOps Logo",type:"text"}],hero:[{section:"hero",key:"title",value:"Unlock Your Ad Revenue Potential with Intelligent Ad Operations",type:"text"},{section:"hero",key:"subtitle",value:"RevAdOps helps publishers and app developers maximize revenue, improve fill rates, and maintain healthy traffic quality through advanced AdTech solutions and data-driven optimization.",type:"text"},{section:"hero",key:"cta_primary_text",value:"Get a Free Consultation",type:"text"},{section:"hero",key:"cta_primary_link",value:"/consultation",type:"text"},{section:"hero",key:"cta_secondary_text",value:"Explore Our Solutions",type:"text"},{section:"hero",key:"cta_secondary_link",value:"/solutions",type:"text"}],what_we_do:[{section:"what_we_do",key:"title",value:"Your Partner in Smarter Ad Monetization",type:"text"},{section:"what_we_do",key:"description",value:"At RevAdOps, we specialize in optimizing web, app, and video monetization strategies for publishers worldwide.",type:"text"},{section:"what_we_do",key:"service_1_title",value:"Revenue Optimization",type:"text"},{section:"what_we_do",key:"service_1_description",value:"Advanced algorithms and real-time bidding strategies to maximize your ad revenue potential.",type:"text"},{section:"what_we_do",key:"service_2_title",value:"Ad Quality Control",type:"text"},{section:"what_we_do",key:"service_2_description",value:"Comprehensive filtering and monitoring to ensure only high-quality ads reach your audience.",type:"text"},{section:"what_we_do",key:"service_3_title",value:"Performance Analytics",type:"text"},{section:"what_we_do",key:"service_3_description",value:"Detailed insights and reporting to track performance and identify optimization opportunities.",type:"text"},{section:"what_we_do",key:"service_4_title",value:"Traffic Protection",type:"text"},{section:"what_we_do",key:"service_4_description",value:"Advanced fraud detection and prevention to protect your traffic quality and advertiser relationships.",type:"text"}],why_choose_us:[{section:"why_choose_us",key:"title",value:"Why Choose RevAdOps?",type:"text"},{section:"why_choose_us",key:"description",value:"We combine cutting-edge technology with industry expertise to deliver exceptional results for our clients.",type:"text"},{section:"why_choose_us",key:"reason_1_title",value:"Proven Results",type:"text"},{section:"why_choose_us",key:"reason_1_description",value:"Average 40% revenue increase within 90 days of implementation.",type:"text"},{section:"why_choose_us",key:"reason_2_title",value:"Expert Team",type:"text"},{section:"why_choose_us",key:"reason_2_description",value:"Dedicated AdTech specialists with 10+ years of industry experience.",type:"text"},{section:"why_choose_us",key:"reason_3_title",value:"Award-Winning",type:"text"},{section:"why_choose_us",key:"reason_3_description",value:"Recognized as a leading ad optimization platform by industry experts.",type:"text"},{section:"why_choose_us",key:"reason_4_title",value:"24/7 Support",type:"text"},{section:"why_choose_us",key:"reason_4_description",value:"Round-the-clock monitoring and support to ensure optimal performance.",type:"text"}],how_it_works:[{section:"how_it_works",key:"title",value:"How It Works",type:"text"},{section:"how_it_works",key:"description",value:"Our streamlined process ensures quick implementation and immediate results.",type:"text"},{section:"how_it_works",key:"step_1_title",value:"Analysis",type:"text"},{section:"how_it_works",key:"step_1_description",value:"We analyze your current ad setup and identify optimization opportunities.",type:"text"},{section:"how_it_works",key:"step_2_title",value:"Strategy",type:"text"},{section:"how_it_works",key:"step_2_description",value:"Develop a customized optimization strategy based on your specific needs.",type:"text"},{section:"how_it_works",key:"step_3_title",value:"Implementation",type:"text"},{section:"how_it_works",key:"step_3_description",value:"Deploy our solutions with minimal disruption to your current operations.",type:"text"},{section:"how_it_works",key:"step_4_title",value:"Optimization",type:"text"},{section:"how_it_works",key:"step_4_description",value:"Continuous monitoring and optimization to maximize your revenue potential.",type:"text"}],our_expertise:[{section:"our_expertise",key:"title",value:"Our Expertise",type:"text"},{section:"our_expertise",key:"description",value:"Deep knowledge across all major ad platforms and technologies.",type:"text"},{section:"our_expertise",key:"expertise_1_title",value:"Programmatic Advertising",type:"text"},{section:"our_expertise",key:"expertise_1_description",value:"Advanced programmatic strategies and real-time bidding optimization.",type:"text"},{section:"our_expertise",key:"expertise_2_title",value:"Header Bidding",type:"text"},{section:"our_expertise",key:"expertise_2_description",value:"Implementation and optimization of header bidding solutions.",type:"text"},{section:"our_expertise",key:"expertise_3_title",value:"Ad Quality & Fraud Prevention",type:"text"},{section:"our_expertise",key:"expertise_3_description",value:"Comprehensive ad quality control and fraud detection systems.",type:"text"}],testimonials:[{section:"testimonials",key:"title",value:"What Our Clients Say",type:"text"},{section:"testimonials",key:"description",value:"Hear from publishers who have transformed their ad revenue with RevAdOps.",type:"text"},{section:"testimonials",key:"testimonial_1_text",value:"RevAdOps increased our ad revenue by 45% in just 3 months. Their team is incredibly knowledgeable and responsive.",type:"text"},{section:"testimonials",key:"testimonial_1_author",value:"Sarah Johnson",type:"text"},{section:"testimonials",key:"testimonial_1_company",value:"TechNews Daily",type:"text"},{section:"testimonials",key:"testimonial_2_text",value:"The fraud detection capabilities saved us thousands in invalid traffic. Highly recommend their services.",type:"text"},{section:"testimonials",key:"testimonial_2_author",value:"Mike Chen",type:"text"},{section:"testimonials",key:"testimonial_2_company",value:"Gaming Hub",type:"text"},{section:"testimonials",key:"testimonial_3_text",value:"Professional service and outstanding results. Our fill rates improved dramatically.",type:"text"},{section:"testimonials",key:"testimonial_3_author",value:"Lisa Rodriguez",type:"text"},{section:"testimonials",key:"testimonial_3_company",value:"Mobile App Co.",type:"text"}],partners:[{section:"partners",key:"title",value:"Trusted by Leading Publishers",type:"text"},{section:"partners",key:"description",value:"Join hundreds of successful publishers who trust RevAdOps for their ad revenue optimization.",type:"text"},{section:"partners",key:"partner_1_name",value:"Partner 1",type:"text"},{section:"partners",key:"partner_2_name",value:"Partner 2",type:"text"},{section:"partners",key:"partner_3_name",value:"Partner 3",type:"text"},{section:"partners",key:"partner_4_name",value:"Partner 4",type:"text"},{section:"partners",key:"partner_5_name",value:"Partner 5",type:"text"},{section:"partners",key:"partner_6_name",value:"Partner 6",type:"text"}],final_cta:[{section:"final_cta",key:"title",value:"Ready to Maximize Your Ad Revenue?",type:"text"},{section:"final_cta",key:"description",value:"Join hundreds of publishers who have increased their revenue with RevAdOps. Get started with a free consultation today.",type:"text"},{section:"final_cta",key:"cta_primary_text",value:"Get Free Consultation",type:"text"},{section:"final_cta",key:"cta_primary_link",value:"/consultation",type:"text"},{section:"final_cta",key:"cta_secondary_text",value:"Contact Us",type:"text"},{section:"final_cta",key:"cta_secondary_link",value:"/contact",type:"text"}],footer:[{section:"footer",key:"logo",value:"/revadops-logo.png",type:"text"},{section:"footer",key:"logo_alt",value:"RevAdOps Logo",type:"text"},{section:"footer",key:"company_description",value:"RevAdOps - Your trusted partner in ad revenue optimization and traffic quality management.",type:"text"},{section:"footer",key:"facebook_link",value:"#",type:"text"},{section:"footer",key:"twitter_link",value:"#",type:"text"},{section:"footer",key:"linkedin_link",value:"#",type:"text"},{section:"footer",key:"instagram_link",value:"#",type:"text"},{section:"footer",key:"copyright_text",value:"\xa9 2024 RevAdOps. All rights reserved.",type:"text"}]},f={};Object.keys(e).forEach(a=>{f[a]=e[a].map(b=>({...b,value:d[a]?.[b.key]||b.value}))}),b(f),i(JSON.parse(JSON.stringify(f)))}catch(a){console.error("Failed to fetch homepage content:",a)}finally{k(!1)}},q=(a,c,d)=>{b(b=>({...b,[a]:b[a].map(a=>a.key===c?{...a,value:d}:a)}))},r=async()=>{m(!0);try{let b=localStorage.getItem("adminToken"),d=[];if(Object.entries(a).forEach(([a,b])=>{b.forEach(b=>{let e=c[a]?.find(a=>a.key===b.key);e&&e.value===b.value||d.push({section:b.section,key:b.key,value:b.value,type:b.type,metadata:b.metadata||{},order:b.order||0})})}),0===d.length){alert("No changes to save."),m(!1);return}console.log(`Saving ${d.length} changed items:`,d);let e=await fetch("http://localhost:5001/api/admin/homepage/bulk",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b}`},body:JSON.stringify({updates:d})});if(e.ok)alert(`Content saved successfully! Updated ${d.length} items.`),await p();else{let a=await e.json();console.error("Save error:",a),alert("Failed to save content: "+(a.message||"Unknown error"))}}catch(a){console.error("Save error:",a),alert("Failed to save content. Please try again.")}finally{m(!1)}},s=[{id:"header",name:"Header",icon:f.A},{id:"hero",name:"Hero Section",icon:f.A},{id:"what_we_do",name:"What We Do",icon:f.A},{id:"why_choose_us",name:"Why Choose Us",icon:f.A},{id:"how_it_works",name:"How It Works",icon:f.A},{id:"our_expertise",name:"Our Expertise",icon:f.A},{id:"testimonials",name:"Testimonials",icon:f.A},{id:"partners",name:"Partners & Clients",icon:f.A},{id:"final_cta",name:"Final CTA",icon:f.A},{id:"footer",name:"Footer & Social",icon:f.A}];return j?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Homepage Content"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Manage your homepage content (images are now static)"})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsxs)("a",{href:"/",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,d.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Preview"]}),(0,d.jsxs)("button",{onClick:r,disabled:l,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[(0,d.jsx)(h.A,{className:"h-4 w-4 mr-2"}),l?"Saving...":"Save Changes"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsx)("nav",{className:"space-y-1",children:s.map(a=>{let b=a.icon;return(0,d.jsxs)("button",{onClick:()=>o(a.id),className:`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${n===a.id?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,d.jsx)(b,{className:"h-4 w-4 mr-3"}),a.name]},a.id)})})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:s.find(a=>a.id===n)?.name}),(0,d.jsx)("div",{className:"space-y-6",children:a[n]?.map(a=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 capitalize",children:a.key.replace(/_/g," ")}),a.key.includes("description")||a.key.includes("subtitle")?(0,d.jsx)("textarea",{value:a.value,onChange:b=>q(a.section,a.key,b.target.value),rows:3,className:"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",placeholder:`Enter ${a.key.replace(/_/g," ")}`}):(0,d.jsx)("input",{type:"text",value:a.value,onChange:b=>q(a.section,a.key,b.target.value),className:"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",placeholder:`Enter ${a.key.replace(/_/g," ")}`})]},`${a.section}-${a.key}`))})]})})]})]})}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,24,602,386],()=>b(b.s=92159));module.exports=c})();