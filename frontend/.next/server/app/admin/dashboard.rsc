1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[4970,[],"ClientSegmentRoot"]
5:I[7018,["810","static/chunks/810-81b2573c23f05abb.js","581","static/chunks/app/admin/layout-35ad107043ab18a2.js"],"default"]
7:I[894,[],"ClientPageRoot"]
8:I[4163,["957","static/chunks/app/admin/dashboard/page-fd0bfba7530baf44.js"],"default"]
b:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[9665,[],"MetadataBoundary"]
12:"$Sreact.suspense"
14:I[8393,[],""]
:HL["/_next/static/css/d09fd67f1bc7ef2a.css","style"]
0:{"P":null,"b":"JYn6I2ExBfBPFhS4cdw8y","p":"","c":["","admin","dashboard"],"i":false,"f":[[["",{"children":["admin",{"children":["dashboard",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/d09fd67f1bc7ef2a.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["admin",["$","$1","c",{"children":[null,["$","$L4",null,{"Component":"$5","slots":{"children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]},"params":{},"promise":"$@6"}]]}],{"children":["dashboard",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":"$0:f:0:1:2:children:1:props:children:1:props:params","promises":["$@9","$@a"]}],null,["$","$Lb",null,{"children":["$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Lf",null,{"children":"$L10"}],null],["$","$L11",null,{"children":["$","div",null,{"hidden":true,"children":["$","$12",null,{"fallback":null,"children":"$L13"}]}]}]]}],false]],"m":"$undefined","G":["$14",[]],"s":false,"S":true}
6:"$0:f:0:1:2:children:1:props:children:1:props:params"
9:{}
a:"$0:f:0:1:2:children:1:props:children:1:props:params"
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
15:I[8175,[],"IconMark"]
e:{"metadata":[["$","title","0",{"children":"RevAdOps - Unlock Your Ad Revenue Potential with Intelligent Ad Operations"}],["$","meta","1",{"name":"description","content":"RevAdOps helps publishers and app developers maximize revenue, improve fill rates, and maintain healthy traffic quality through advanced AdTech solutions and data-driven optimization."}],["$","meta","2",{"name":"author","content":"RevAdOps Team"}],["$","meta","3",{"name":"keywords","content":"ad revenue, programmatic advertising, header bidding, ad monetization, publishers, app developers, adtech"}],["$","meta","4",{"name":"robots","content":"index, follow"}],["$","link","5",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","link","6",{"rel":"icon","href":"/favicon.png","sizes":"32x32","type":"image/png"}],["$","link","7",{"rel":"icon","href":"/revadops-logo.png","sizes":"192x192","type":"image/png"}],["$","link","8",{"rel":"apple-touch-icon","href":"/revadops-logo.png","sizes":"180x180","type":"image/png"}],["$","$L15","9",{}]],"error":null,"digest":"$undefined"}
13:"$e:metadata"
