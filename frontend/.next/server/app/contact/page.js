(()=>{var a={};a.id=977,a.ids=[977],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29300:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>x});var d=c(60687),e=c(48340),f=c(19169),g=c(62688);let h=(0,g.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var i=c(48730);function j({content:a}){let b=[{icon:e.A,label:"Phone",value:a.phone||"+****************",href:`tel:${a.phone||"+***********"}`},{icon:f.A,label:"Email",value:a.email||"<EMAIL>",href:`mailto:${a.email||"<EMAIL>"}`},{icon:h,label:"Address",value:a.address||"123 Business St, Suite 100, City, State 12345",href:"#map"},{icon:i.A,label:"Business Hours",value:a.hours||"Mon-Fri: 9AM-6PM EST",href:null}];return(0,d.jsxs)("section",{className:"relative bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white py-20 lg:py-32",style:{backgroundImage:a.background_image?`linear-gradient(rgba(30, 58, 138, 0.8), rgba(67, 56, 202, 0.8)), url(${a.background_image})`:void 0,backgroundSize:"cover",backgroundPosition:"center"},children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-black opacity-20"}),(0,d.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,d.jsxs)("div",{children:[a.subtitle&&(0,d.jsx)("p",{className:"text-blue-200 text-lg font-medium mb-4",children:a.subtitle}),(0,d.jsx)("h1",{className:"text-4xl lg:text-6xl font-bold mb-6 leading-tight",children:a.title||"Get in Touch"}),(0,d.jsx)("p",{className:"text-xl text-blue-100 mb-8 leading-relaxed",children:a.description||"Ready to take your digital marketing to the next level? We'd love to hear from you."})]}),(0,d.jsxs)("div",{className:"bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold mb-6",children:"Contact Information"}),(0,d.jsx)("div",{className:"space-y-6",children:b.map((a,b)=>{let c=a.icon,e=(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"flex-shrink-0 w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4",children:(0,d.jsx)(c,{className:"h-6 w-6 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-blue-200 mb-1",children:a.label}),(0,d.jsx)("div",{className:"text-white font-medium",children:a.value})]})]});return(0,d.jsx)("div",{children:a.href?(0,d.jsx)("a",{href:a.href,className:"block hover:bg-white hover:bg-opacity-5 rounded-lg p-2 -m-2 transition-colors duration-200",children:e}):(0,d.jsx)("div",{className:"p-2 -m-2",children:e})},b)})})]})]})})]})}var k=c(33872),l=c(41312),m=c(86561);let n=(0,g.A)("headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),o={phone:e.A,mail:f.A,map:h,clock:i.A,message:k.A,users:l.A,award:m.A,headphones:n};function p({content:a}){let b=[{name:a.office_1_name||"Main Office",address:a.office_1_address||"123 Business Street, Suite 100\nNew York, NY 10001",phone:a.office_1_phone||"+****************",email:a.office_1_email||"<EMAIL>"},{name:a.office_2_name||"West Coast Office",address:a.office_2_address||"456 Innovation Ave, Floor 5\nSan Francisco, CA 94105",phone:a.office_2_phone||"+****************",email:a.office_2_email||"<EMAIL>"}],c=[{title:a.feature_1_title||"24/7 Support",description:a.feature_1_description||"Round-the-clock assistance for all your needs.",icon:a.feature_1_icon||"headphones"},{title:a.feature_2_title||"Expert Team",description:a.feature_2_description||"Experienced professionals ready to help.",icon:a.feature_2_icon||"users"},{title:a.feature_3_title||"Quick Response",description:a.feature_3_description||"Fast response times to your inquiries.",icon:a.feature_3_icon||"message"},{title:a.feature_4_title||"Proven Results",description:a.feature_4_description||"Track record of successful partnerships.",icon:a.feature_4_icon||"award"}];return(0,d.jsx)("section",{className:"py-20 bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[a.subtitle&&(0,d.jsx)("p",{className:"text-blue-600 font-semibold text-lg mb-4",children:a.subtitle}),(0,d.jsx)("h2",{className:"text-4xl lg:text-5xl font-bold text-gray-900 mb-6",children:a.title||"Multiple Ways to Reach Us"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:a.description||"Choose the most convenient way to get in touch with our team."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8 mb-16",children:[(0,d.jsxs)("div",{className:"lg:col-span-2",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-8",children:"Our Offices"}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 gap-8",children:b.map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg",children:[(0,d.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-4",children:a.name}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(h,{className:"h-5 w-5 text-blue-600 mr-3 mt-1 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Address"}),(0,d.jsx)("div",{className:"text-gray-900 whitespace-pre-line",children:a.address})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(e.A,{className:"h-5 w-5 text-blue-600 mr-3 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Phone"}),(0,d.jsx)("a",{href:`tel:${a.phone}`,className:"text-gray-900 hover:text-blue-600 transition-colors",children:a.phone})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(f.A,{className:"h-5 w-5 text-blue-600 mr-3 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Email"}),(0,d.jsx)("a",{href:`mailto:${a.email}`,className:"text-gray-900 hover:text-blue-600 transition-colors",children:a.email})]})]})]})]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-8",children:"Support Center"}),(0,d.jsxs)("div",{className:"bg-blue-600 text-white rounded-xl p-8",children:[(0,d.jsx)("h4",{className:"text-xl font-bold mb-4",children:a.support_title||"Need Help?"}),(0,d.jsx)("p",{className:"text-blue-100 mb-6",children:a.support_description||"Our support team is here to assist you with any questions or concerns."}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(i.A,{className:"h-5 w-5 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-blue-200",children:"Hours"}),(0,d.jsx)("div",{children:a.support_hours||"Mon-Fri: 9AM-6PM EST"})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(e.A,{className:"h-5 w-5 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-blue-200",children:"Support Phone"}),(0,d.jsx)("a",{href:`tel:${a.support_phone||"+***********"}`,className:"hover:text-blue-200 transition-colors",children:a.support_phone||"+****************"})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(f.A,{className:"h-5 w-5 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-blue-200",children:"Support Email"}),(0,d.jsx)("a",{href:`mailto:${a.support_email||"<EMAIL>"}`,className:"hover:text-blue-200 transition-colors",children:a.support_email||"<EMAIL>"})]})]})]})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-8 text-center",children:"Why Choose Us"}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:c.map((a,b)=>{let c=o[a.icon]||n;return(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4",children:(0,d.jsx)(c,{className:"h-8 w-8 text-blue-600"})}),(0,d.jsx)("h4",{className:"text-lg font-bold text-gray-900 mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600",children:a.description})]},b)})})]})]})})}var q=c(43210),r=c(5336),s=c(58869);let t=(0,g.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);function u({content:a}){let[b,c]=(0,q.useState)({name:"",email:"",phone:"",company:"",subject:"",service:"",budget:"",timeline:"",message:""}),[g,h]=(0,q.useState)(!1),[i,j]=(0,q.useState)(!1),l=a.service_options?a.service_options.split(",").map(a=>a.trim()):["SEO Optimization","PPC Advertising","Social Media Marketing","Content Marketing","Email Marketing","Analytics & Reporting"],m=a.budget_options?a.budget_options.split(",").map(a=>a.trim()):["Under $5,000","$5,000 - $10,000","$10,000 - $25,000","$25,000 - $50,000","$50,000+"],n=a.timeline_options?a.timeline_options.split(",").map(a=>a.trim()):["ASAP","1-3 months","3-6 months","6+ months"],o=a=>{let{name:b,value:d}=a.target;c(a=>({...a,[b]:d}))},p=async a=>{a.preventDefault(),h(!0);try{await new Promise(a=>setTimeout(a,2e3)),console.log("Form submitted:",b),j(!0),c({name:"",email:"",phone:"",company:"",subject:"",service:"",budget:"",timeline:"",message:""})}catch(a){console.error("Form submission error:",a)}finally{h(!1)}};return i?(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsx)("div",{className:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,d.jsxs)("div",{className:"bg-green-50 rounded-2xl p-12",children:[(0,d.jsx)(r.A,{className:"h-16 w-16 text-green-600 mx-auto mb-6"}),(0,d.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Thank You!"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:a.success_message||"Your message has been sent successfully. We'll get back to you within 24 hours."}),(0,d.jsx)("button",{onClick:()=>j(!1),className:"px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200",children:"Send Another Message"})]})})}):(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[a.subtitle&&(0,d.jsx)("p",{className:"text-blue-600 font-semibold text-lg mb-4",children:a.subtitle}),(0,d.jsx)("h2",{className:"text-4xl lg:text-5xl font-bold text-gray-900 mb-6",children:a.title||"Send Us a Message"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:a.description||"Fill out the form below and we'll get back to you as soon as possible."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-start",children:[(0,d.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:a.form_title||"Get in Touch"}),(0,d.jsx)("p",{className:"text-gray-600 mb-8",children:a.form_description||"Tell us about your project and how we can help."}),(0,d.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(s.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,d.jsx)("input",{type:"text",id:"name",name:"name",value:b.name,onChange:o,placeholder:a.name_placeholder||"Your full name",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,d.jsx)("input",{type:"email",id:"email",name:"email",value:b.email,onChange:o,placeholder:a.email_placeholder||"<EMAIL>",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0})]})]})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(e.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,d.jsx)("input",{type:"tel",id:"phone",name:"phone",value:b.phone,onChange:o,placeholder:a.phone_placeholder||"+****************",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-2",children:"Company"}),(0,d.jsx)("input",{type:"text",id:"company",name:"company",value:b.company,onChange:o,placeholder:a.company_placeholder||"Your company name",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject *"}),(0,d.jsx)("input",{type:"text",id:"subject",name:"subject",value:b.subject,onChange:o,placeholder:a.subject_placeholder||"What can we help you with?",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"service",className:"block text-sm font-medium text-gray-700 mb-2",children:"Service Interested In"}),(0,d.jsxs)("select",{id:"service",name:"service",value:b.service,onChange:o,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"Select a service"}),l.map((a,b)=>(0,d.jsx)("option",{value:a,children:a},b))]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"budget",className:"block text-sm font-medium text-gray-700 mb-2",children:"Budget Range"}),(0,d.jsxs)("select",{id:"budget",name:"budget",value:b.budget,onChange:o,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"Select budget"}),m.map((a,b)=>(0,d.jsx)("option",{value:a,children:a},b))]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"timeline",className:"block text-sm font-medium text-gray-700 mb-2",children:"Timeline"}),(0,d.jsxs)("select",{id:"timeline",name:"timeline",value:b.timeline,onChange:o,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"Select timeline"}),n.map((a,b)=>(0,d.jsx)("option",{value:a,children:a},b))]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(k.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,d.jsx)("textarea",{id:"message",name:"message",value:b.message,onChange:o,rows:6,placeholder:a.message_placeholder||"Tell us more about your project...",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",required:!0})]})]}),(0,d.jsx)("button",{type:"submit",disabled:g,className:"w-full px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center",children:g?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Sending..."]}):(0,d.jsxs)(d.Fragment,{children:[a.submit_button_text||"Send Message",(0,d.jsx)(t,{className:"ml-2 h-5 w-5"})]})})]})]}),(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"bg-blue-600 text-white rounded-2xl p-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold mb-6",children:"Let's Start a Conversation"}),(0,d.jsx)("p",{className:"text-blue-100 mb-8",children:"Ready to take your digital marketing to the next level? Our team of experts is here to help you achieve your goals."}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),(0,d.jsx)("span",{className:"text-blue-100",children:"Free consultation"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),(0,d.jsx)("span",{className:"text-blue-100",children:"Custom strategy development"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),(0,d.jsx)("span",{className:"text-blue-100",children:"Dedicated account manager"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),(0,d.jsx)("span",{className:"text-blue-100",children:"Transparent reporting"})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8",children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Quick Response"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"We typically respond to all inquiries within 2-4 hours during business hours."}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Business Hours: Monday - Friday, 9AM - 6PM EST"})]})]})]})]})})}let v=(0,g.A)("navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);function w({content:a}){let b=[{name:a.location_1_name||"New York Office",address:a.location_1_address||"123 Business Street, Suite 100\nNew York, NY 10001",phone:a.location_1_phone||"+****************",email:a.location_1_email||"<EMAIL>",hours:a.location_1_hours||"Mon-Fri: 9AM-6PM EST",lat:a.location_1_lat||"40.7128",lng:a.location_1_lng||"-74.0060"},{name:a.location_2_name||"San Francisco Office",address:a.location_2_address||"456 Innovation Avenue, Floor 5\nSan Francisco, CA 94105",phone:a.location_2_phone||"+****************",email:a.location_2_email||"<EMAIL>",hours:a.location_2_hours||"Mon-Fri: 9AM-6PM PST",lat:a.location_2_lat||"37.7749",lng:a.location_2_lng||"-122.4194"}].filter(a=>a.name&&a.address),c=()=>{if(a.map_embed_url)return a.map_embed_url;let c=b[0];if(c){let a=encodeURIComponent(c.address.replace("\n",", "));return`https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${a}`}return null};return(0,d.jsx)("section",{id:"map",className:"py-20 bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[a.subtitle&&(0,d.jsx)("p",{className:"text-blue-600 font-semibold text-lg mb-4",children:a.subtitle}),(0,d.jsx)("h2",{className:"text-4xl lg:text-5xl font-bold text-gray-900 mb-6",children:a.title||"Visit Our Offices"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:a.description||"Find us at one of our convenient locations or schedule a virtual meeting."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,d.jsx)("div",{className:"lg:col-span-1 space-y-6",children:b.map((b,c)=>(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:b.name}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(h,{className:"h-5 w-5 text-blue-600 mr-3 mt-1 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Address"}),(0,d.jsx)("div",{className:"text-gray-900 whitespace-pre-line",children:b.address})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(e.A,{className:"h-5 w-5 text-blue-600 mr-3 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Phone"}),(0,d.jsx)("a",{href:`tel:${b.phone}`,className:"text-gray-900 hover:text-blue-600 transition-colors",children:b.phone})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(f.A,{className:"h-5 w-5 text-blue-600 mr-3 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Email"}),(0,d.jsx)("a",{href:`mailto:${b.email}`,className:"text-gray-900 hover:text-blue-600 transition-colors",children:b.email})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(i.A,{className:"h-5 w-5 text-blue-600 mr-3 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Hours"}),(0,d.jsx)("div",{className:"text-gray-900",children:b.hours})]})]})]}),"false"!==a.show_directions&&(0,d.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,d.jsxs)("a",{href:(a=>{let b=encodeURIComponent(a.replace("\n",", "));return`https://www.google.com/maps/dir/?api=1&destination=${b}`})(b.address),target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors duration-200",children:[(0,d.jsx)(v,{className:"h-4 w-4 mr-2"}),"Get Directions"]})})]},c))}),(0,d.jsx)("div",{className:"lg:col-span-2",children:(0,d.jsx)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden h-96 lg:h-full min-h-[400px]",children:c()?(0,d.jsx)("iframe",{src:c(),width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"Office Location Map"}):(0,d.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gray-100",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(h,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-600 mb-2",children:"Interactive Map"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Map integration requires API configuration"}),(0,d.jsx)("div",{className:"mt-4 space-y-2",children:b.map((a,b)=>(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,d.jsxs)("strong",{children:[a.name,":"]})," ",a.address.replace("\n",", ")]},b))})]})})})})]}),(0,d.jsx)("div",{className:"mt-16 text-center",children:(0,d.jsxs)("div",{className:"bg-blue-600 text-white rounded-2xl p-8 max-w-4xl mx-auto",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Can't Visit in Person?"}),(0,d.jsx)("p",{className:"text-blue-100 mb-6",children:"No problem! We offer virtual consultations and meetings to accommodate your schedule."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)("a",{href:"#contact-form",className:"inline-flex items-center justify-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors duration-200",children:"Schedule Virtual Meeting"}),(0,d.jsxs)("a",{href:"tel:+***********",className:"inline-flex items-center justify-center px-6 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors duration-200",children:[(0,d.jsx)(e.A,{className:"h-4 w-4 mr-2"}),"Call Now"]})]})]})})]})})}function x(){let[a,b]=(0,q.useState)({hero:{},info:{},form:{},map:{}}),[c,e]=(0,q.useState)(!0);return c?(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):a?(0,d.jsxs)("div",{className:"min-h-screen",children:[(0,d.jsx)(j,{content:a.hero}),(0,d.jsx)(p,{content:a.info}),(0,d.jsx)(u,{content:a.form}),(0,d.jsx)(w,{content:a.map})]}):(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Content Not Available"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Unable to load contact content."})]})})}},33748:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},33872:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]])},33873:a=>{"use strict";a.exports=require("path")},40476:()=>{},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41312:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43839:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/contact/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/contact/page.tsx","default")},48340:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},54044:()=>{},57716:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},58869:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},61135:()=>{},62688:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(43210);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68761:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,43839)),"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/contact/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/contact/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/contact/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},78876:(a,b,c)=>{Promise.resolve().then(c.bind(c,29300))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},92444:(a,b,c)=>{Promise.resolve().then(c.bind(c,43839))},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(37413);c(61135);let e={title:"RevAdOps - Unlock Your Ad Revenue Potential with Intelligent Ad Operations",description:"RevAdOps helps publishers and app developers maximize revenue, improve fill rates, and maintain healthy traffic quality through advanced AdTech solutions and data-driven optimization.",keywords:"ad revenue, programmatic advertising, header bidding, ad monetization, publishers, app developers, adtech",authors:[{name:"RevAdOps Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",icons:{icon:[{url:"/favicon.png",sizes:"32x32",type:"image/png"},{url:"/revadops-logo.png",sizes:"192x192",type:"image/png"}],apple:[{url:"/revadops-logo.png",sizes:"180x180",type:"image/png"}]}};function f({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{children:a})})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,24],()=>b(b.s=68761));module.exports=c})();