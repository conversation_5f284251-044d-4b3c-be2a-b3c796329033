{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Menu, X } from 'lucide-react';\n\n\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Services', href: '/services' },\n    { name: 'Blog', href: '/blog' },\n    { name: 'Contact', href: '/contact' },\n  ];\n\n  return (\n    <header className=\"bg-white shadow-sm sticky top-0 z-50\">\n      <div className=\"container-custom\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <Image\n              src=\"/logo.png\"\n              alt=\"RevAdOps Logo\"\n              width={120}\n              height={40}\n              className=\"h-10 w-auto\"\n            />\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200\"\n              >\n                {item.name}\n              </Link>\n            ))}\n            <Link\n              href=\"/consultation\"\n              className=\"btn-primary\"\n            >\n              Get Free Consultation\n            </Link>\n          </nav>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden p-2\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            aria-label=\"Toggle menu\"\n          >\n            {isMenuOpen ? (\n              <X className=\"h-6 w-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"h-6 w-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 py-2\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <Link\n                href=\"/consultation\"\n                className=\"btn-primary w-fit mt-4\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Get Free Consultation\n              </Link>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AASA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;8CAOlB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;4BAC9B,cAAW;sCAEV,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;yFAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useState, useEffect } from 'react';\n\n\ninterface FooterContent {\n  logo?: string;\n  logo_alt?: string;\n  company_description?: string;\n  facebook_icon?: string;\n  facebook_link?: string;\n  twitter_icon?: string;\n  twitter_link?: string;\n  linkedin_icon?: string;\n  linkedin_link?: string;\n  instagram_icon?: string;\n  instagram_link?: string;\n  copyright_text?: string;\n}\n\nconst Footer = () => {\n\n  const [footerContent, setFooterContent] = useState<FooterContent>({});\n\n  useEffect(() => {\n    fetchFooterContent();\n  }, []);\n\n  const fetchFooterContent = async () => {\n    try {\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/content`);\n      if (response.ok) {\n        const data = await response.json();\n        setFooterContent(data.footer || {});\n      }\n    } catch (error) {\n      console.error('Failed to fetch footer content:', error);\n    }\n  };\n\n\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container-custom\">\n        <div className=\"py-8\">\n          {/* Logo Section */}\n          <div className=\"text-center mb-8\">\n            <Link href=\"/\" className=\"inline-flex items-center space-x-3\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"RevAdOps Logo\"\n                width={96}\n                height={32}\n                className=\"h-8 w-auto\"\n              />\n            </Link>\n            {footerContent.company_description && (\n              <p className=\"text-gray-400 mt-4 max-w-md mx-auto\">\n                {footerContent.company_description}\n              </p>\n            )}\n          </div>\n\n          {/* Bottom Section - ITAO Style */}\n          <div className=\"border-t border-gray-800 pt-8\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 text-center md:text-left\">\n              <p className=\"text-gray-400 text-sm\">\n                Copyright © {new Date().getFullYear()} RevAdOps. All Rights Reserved\n              </p>\n\n              <div className=\"flex items-center space-x-4\">\n                <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm\">\n                  | Privacy Policy\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAsBA,MAAM,SAAS;IAEb,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,CAAC;IAEnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iEAAmC,QAAQ,CAAC;YACzE,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB,KAAK,MAAM,IAAI,CAAC;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAIA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;4BAGb,cAAc,mBAAmB,kBAChC,8OAAC;gCAAE,WAAU;0CACV,cAAc,mBAAmB;;;;;;;;;;;;kCAMxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAwB;wCACtB,IAAI,OAAO,WAAW;wCAAG;;;;;;;8CAGxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzF;uCAEe", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHeroSection.tsx"], "sourcesContent": ["'use client';\n\n\nimport Link from 'next/link';\nimport { ArrowRight } from 'lucide-react';\n\ninterface HeroContent {\n  title?: string;\n  subtitle?: string;\n  background_image?: string;\n  cta_primary_text?: string;\n  cta_primary_link?: string;\n  cta_secondary_text?: string;\n  cta_secondary_link?: string;\n}\n\ninterface DynamicHeroSectionProps {\n  content: HeroContent;\n}\n\nconst DynamicHeroSection = ({ content }: DynamicHeroSectionProps) => {\n  // Default content fallback\n  const defaultContent = {\n    title: \"Unlock Your Ad Revenue Potential with Intelligent Ad Operations\",\n    subtitle: \"RevAdOps helps publishers and app developers maximize revenue, improve fill rates, and maintain healthy traffic quality through advanced AdTech solutions and data-driven optimization.\",\n    background_image: \"https://images.unsplash.com/photo-**********-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    cta_primary_text: \"Get a Free Consultation\",\n    cta_primary_link: \"/consultation\",\n    cta_secondary_text: \"Learn More\",\n    cta_secondary_link: \"/about\"\n  };\n\n  // Merge content with defaults\n  const heroData = { ...defaultContent, ...content };\n\n\n\n  return (\n    <section className=\"relative min-h-screen bg-white\">\n      {/* Hero Banner Image - ITAO Style */}\n      <div className=\"relative h-screen overflow-hidden\">\n        <div\n          className=\"absolute inset-0 bg-cover bg-center bg-no-repeat bg-gradient-to-br from-blue-900 to-purple-900\"\n          style={{\n            backgroundImage: heroData.background_image ? `url(${heroData.background_image})` : 'none'\n          }}\n        >\n\n          {/* Dark overlay for better text readability */}\n          <div className=\"absolute inset-0 bg-black bg-opacity-40\"></div>\n        </div>\n\n        {/* Content overlay */}\n        <div className=\"relative z-10 flex items-center justify-center h-full\">\n          <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <div className=\"max-w-4xl mx-auto\">\n              <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight\">\n                {heroData.title}\n              </h1>\n              <p className=\"text-xl md:text-2xl text-gray-200 mb-8 leading-relaxed max-w-3xl mx-auto\">\n                {heroData.subtitle}\n              </p>\n              \n              {/* CTA Buttons */}\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n                <Link\n                  href={heroData.cta_primary_link || '/consultation'}\n                  className=\"inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n                >\n                  {heroData.cta_primary_text}\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Link>\n                \n                {heroData.cta_secondary_text && (\n                  <Link\n                    href={heroData.cta_secondary_link || '/about'}\n                    className=\"inline-flex items-center px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-gray-900 transition-all duration-200\"\n                  >\n                    {heroData.cta_secondary_text}\n                  </Link>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n      </div>\n\n      {/* Scroll indicator */}\n      <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default DynamicHeroSection;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAoBA,MAAM,qBAAqB,CAAC,EAAE,OAAO,EAA2B;IAC9D,2BAA2B;IAC3B,MAAM,iBAAiB;QACrB,OAAO;QACP,UAAU;QACV,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,oBAAoB;IACtB;IAEA,8BAA8B;IAC9B,MAAM,WAAW;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IAIjD,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,SAAS,gBAAgB,GAAG,CAAC,IAAI,EAAE,SAAS,gBAAgB,CAAC,CAAC,CAAC,GAAG;wBACrF;kCAIA,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,SAAS,KAAK;;;;;;kDAEjB,8OAAC;wCAAE,WAAU;kDACV,SAAS,QAAQ;;;;;;kDAIpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,SAAS,gBAAgB,IAAI;gDACnC,WAAU;;oDAET,SAAS,gBAAgB;kEAC1B,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;4CAGvB,SAAS,kBAAkB,kBAC1B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,SAAS,kBAAkB,IAAI;gDACrC,WAAU;0DAET,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAY1C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhatWeDoSection.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\n\nimport { TrendingUp, Target, BarChart3, Shield } from 'lucide-react';\n\ninterface WhatWeDoContent {\n  title?: string;\n  description?: string;\n  service_1_title?: string;\n  service_1_description?: string;\n  service_1_icon?: string;\n  service_2_title?: string;\n  service_2_description?: string;\n  service_2_icon?: string;\n  service_3_title?: string;\n  service_3_description?: string;\n  service_3_icon?: string;\n  service_4_title?: string;\n  service_4_description?: string;\n  service_4_icon?: string;\n}\n\ninterface DynamicWhatWeDoSectionProps {\n  content: WhatWeDoContent;\n}\n\nconst DynamicWhatWeDoSection = ({ content }: DynamicWhatWeDoSectionProps) => {\n  // Default content fallback\n  const defaultContent = {\n    title: \"What We Do\",\n    description: \"We provide comprehensive ad revenue optimization solutions that help publishers and app developers maximize their earnings while maintaining excellent user experience.\",\n    service_1_title: \"Revenue Optimization\",\n    service_1_description: \"Advanced algorithms and real-time bidding strategies to maximize your ad revenue potential.\",\n    service_2_title: \"Ad Quality Control\",\n    service_2_description: \"Comprehensive filtering and monitoring to ensure only high-quality ads reach your audience.\",\n    service_3_title: \"Performance Analytics\",\n    service_3_description: \"Detailed insights and reporting to track performance and identify optimization opportunities.\",\n    service_4_title: \"Traffic Protection\",\n    service_4_description: \"Advanced fraud detection and prevention to protect your traffic quality and advertiser relationships.\"\n  };\n\n  // Merge content with defaults\n  const sectionData = { ...defaultContent, ...content };\n\n  const services = [\n    {\n      icon: sectionData.service_1_icon || TrendingUp,\n      title: sectionData.service_1_title,\n      description: sectionData.service_1_description,\n      color: \"bg-blue-500\"\n    },\n    {\n      icon: sectionData.service_2_icon || Target,\n      title: sectionData.service_2_title,\n      description: sectionData.service_2_description,\n      color: \"bg-green-500\"\n    },\n    {\n      icon: sectionData.service_3_icon || BarChart3,\n      title: sectionData.service_3_title,\n      description: sectionData.service_3_description,\n      color: \"bg-purple-500\"\n    },\n    {\n      icon: sectionData.service_4_icon || Shield,\n      title: sectionData.service_4_title,\n      description: sectionData.service_4_description,\n      color: \"bg-orange-500\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            {sectionData.title}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            {sectionData.description}\n          </p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {services.map((service, index) => {\n            const isImageIcon = typeof service.icon === 'string';\n            const Icon = isImageIcon ? null : service.icon;\n\n            return (\n              <div\n                key={index}\n                className=\"bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100\"\n              >\n                <div className={`w-16 h-16 ${service.color} rounded-lg flex items-center justify-center mb-6 mx-auto`}>\n                  {isImageIcon ? (\n                    <Image\n                      src={service.icon as string}\n                      alt={service.title}\n                      width={32}\n                      height={32}\n                      className=\"h-8 w-8 object-contain\"\n                    />\n                  ) : (\n                    Icon && <Icon className=\"h-8 w-8 text-white\" />\n                  )}\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-4 text-center\">\n                  {service.title}\n                </h3>\n                <p className=\"text-gray-600 text-center leading-relaxed\">\n                  {service.description}\n                </p>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-gray-100\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Ready to Optimize Your Ad Revenue?\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              Join hundreds of publishers who have increased their revenue by up to 40% with our proven optimization strategies.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200\">\n                Start Free Analysis\n              </button>\n              <button className=\"px-8 py-3 border-2 border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors duration-200\">\n                View Case Studies\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default DynamicWhatWeDoSection;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAJA;;;;AA2BA,MAAM,yBAAyB,CAAC,EAAE,OAAO,EAA+B;IACtE,2BAA2B;IAC3B,MAAM,iBAAiB;QACrB,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,uBAAuB;QACvB,iBAAiB;QACjB,uBAAuB;QACvB,iBAAiB;QACjB,uBAAuB;QACvB,iBAAiB;QACjB,uBAAuB;IACzB;IAEA,8BAA8B;IAC9B,MAAM,cAAc;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IAEpD,MAAM,WAAW;QACf;YACE,MAAM,YAAY,cAAc,IAAI,kNAAA,CAAA,aAAU;YAC9C,OAAO,YAAY,eAAe;YAClC,aAAa,YAAY,qBAAqB;YAC9C,OAAO;QACT;QACA;YACE,MAAM,YAAY,cAAc,IAAI,sMAAA,CAAA,SAAM;YAC1C,OAAO,YAAY,eAAe;YAClC,aAAa,YAAY,qBAAqB;YAC9C,OAAO;QACT;QACA;YACE,MAAM,YAAY,cAAc,IAAI,kNAAA,CAAA,YAAS;YAC7C,OAAO,YAAY,eAAe;YAClC,aAAa,YAAY,qBAAqB;YAC9C,OAAO;QACT;QACA;YACE,MAAM,YAAY,cAAc,IAAI,sMAAA,CAAA,SAAM;YAC1C,OAAO,YAAY,eAAe;YAClC,aAAa,YAAY,qBAAqB;YAC9C,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,YAAY,KAAK;;;;;;sCAEpB,8OAAC;4BAAE,WAAU;sCACV,YAAY,WAAW;;;;;;;;;;;;8BAK5B,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,cAAc,OAAO,QAAQ,IAAI,KAAK;wBAC5C,MAAM,OAAO,cAAc,OAAO,QAAQ,IAAI;wBAE9C,qBACE,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAW,CAAC,UAAU,EAAE,QAAQ,KAAK,CAAC,yDAAyD,CAAC;8CAClG,4BACC,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,QAAQ,IAAI;wCACjB,KAAK,QAAQ,KAAK;wCAClB,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;mFAGZ,sBAAQ,8OAAC;wCAAK,WAAU;;;;;;;;;;;8CAG5B,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BApBjB;;;;;oBAwBX;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA6G;;;;;;kDAG/H,8OAAC;wCAAO,WAAU;kDAA4H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5J;uCAEe", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhyChooseSection.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\n\nimport { CheckCir<PERSON>, Users, Award, Clock } from 'lucide-react';\n\ninterface WhyChooseContent {\n  title?: string;\n  description?: string;\n  reason_1_title?: string;\n  reason_1_description?: string;\n  reason_1_icon?: string;\n  reason_2_title?: string;\n  reason_2_description?: string;\n  reason_2_icon?: string;\n  reason_3_title?: string;\n  reason_3_description?: string;\n  reason_3_icon?: string;\n  reason_4_title?: string;\n  reason_4_description?: string;\n  reason_4_icon?: string;\n}\n\ninterface DynamicWhyChooseSectionProps {\n  content: WhyChooseContent;\n}\n\nconst DynamicWhyChooseSection = ({ content }: DynamicWhyChooseSectionProps) => {\n  const defaultContent = {\n    title: \"Why Choose RevAdOps?\",\n    description: \"We combine cutting-edge technology with industry expertise to deliver exceptional results for our clients.\",\n    reason_1_title: \"Proven Results\",\n    reason_1_description: \"Average 40% revenue increase within 90 days of implementation.\",\n    reason_2_title: \"Expert Team\",\n    reason_2_description: \"Dedicated AdTech specialists with 10+ years of industry experience.\",\n    reason_3_title: \"Award-Winning\",\n    reason_3_description: \"Recognized as a leading ad optimization platform by industry experts.\",\n    reason_4_title: \"24/7 Support\",\n    reason_4_description: \"Round-the-clock monitoring and support to ensure optimal performance.\"\n  };\n\n  const sectionData = { ...defaultContent, ...content };\n\n  const reasons = [\n    {\n      icon: sectionData.reason_1_icon || CheckCircle,\n      title: sectionData.reason_1_title,\n      description: sectionData.reason_1_description,\n      color: \"bg-green-500\"\n    },\n    {\n      icon: sectionData.reason_2_icon || Users,\n      title: sectionData.reason_2_title,\n      description: sectionData.reason_2_description,\n      color: \"bg-blue-500\"\n    },\n    {\n      icon: sectionData.reason_3_icon || Award,\n      title: sectionData.reason_3_title,\n      description: sectionData.reason_3_description,\n      color: \"bg-purple-500\"\n    },\n    {\n      icon: sectionData.reason_4_icon || Clock,\n      title: sectionData.reason_4_title,\n      description: sectionData.reason_4_description,\n      color: \"bg-orange-500\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            {sectionData.title}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {sectionData.description}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {reasons.map((reason, index) => {\n            const isImageIcon = typeof reason.icon === 'string';\n            const Icon = isImageIcon ? null : reason.icon;\n\n            return (\n              <div key={index} className=\"text-center\">\n                <div className={`w-16 h-16 ${reason.color} rounded-full flex items-center justify-center mb-6 mx-auto`}>\n                  {isImageIcon ? (\n                    <Image\n                      src={reason.icon as string}\n                      alt={reason.title}\n                      width={32}\n                      height={32}\n                      className=\"h-8 w-8 object-contain\"\n                    />\n                  ) : (\n                    Icon && <Icon className=\"h-8 w-8 text-white\" />\n                  )}\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  {reason.title}\n                </h3>\n                <p className=\"text-gray-600\">\n                  {reason.description}\n                </p>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default DynamicWhyChooseSection;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAJA;;;;AA2BA,MAAM,0BAA0B,CAAC,EAAE,OAAO,EAAgC;IACxE,MAAM,iBAAiB;QACrB,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,sBAAsB;QACtB,gBAAgB;QAChB,sBAAsB;QACtB,gBAAgB;QAChB,sBAAsB;QACtB,gBAAgB;QAChB,sBAAsB;IACxB;IAEA,MAAM,cAAc;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IAEpD,MAAM,UAAU;QACd;YACE,MAAM,YAAY,aAAa,IAAI,2NAAA,CAAA,cAAW;YAC9C,OAAO,YAAY,cAAc;YACjC,aAAa,YAAY,oBAAoB;YAC7C,OAAO;QACT;QACA;YACE,MAAM,YAAY,aAAa,IAAI,oMAAA,CAAA,QAAK;YACxC,OAAO,YAAY,cAAc;YACjC,aAAa,YAAY,oBAAoB;YAC7C,OAAO;QACT;QACA;YACE,MAAM,YAAY,aAAa,IAAI,oMAAA,CAAA,QAAK;YACxC,OAAO,YAAY,cAAc;YACjC,aAAa,YAAY,oBAAoB;YAC7C,OAAO;QACT;QACA;YACE,MAAM,YAAY,aAAa,IAAI,oMAAA,CAAA,QAAK;YACxC,OAAO,YAAY,cAAc;YACjC,aAAa,YAAY,oBAAoB;YAC7C,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,YAAY,KAAK;;;;;;sCAEpB,8OAAC;4BAAE,WAAU;sCACV,YAAY,WAAW;;;;;;;;;;;;8BAI5B,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ;wBACpB,MAAM,cAAc,OAAO,OAAO,IAAI,KAAK;wBAC3C,MAAM,OAAO,cAAc,OAAO,OAAO,IAAI;wBAE7C,qBACE,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAW,CAAC,UAAU,EAAE,OAAO,KAAK,CAAC,2DAA2D,CAAC;8CACnG,4BACC,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,OAAO,IAAI;wCAChB,KAAK,OAAO,KAAK;wCACjB,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;mFAGZ,sBAAQ,8OAAC;wCAAK,WAAU;;;;;;;;;;;8CAG5B,8OAAC;oCAAG,WAAU;8CACX,OAAO,KAAK;;;;;;8CAEf,8OAAC;oCAAE,WAAU;8CACV,OAAO,WAAW;;;;;;;2BAlBb;;;;;oBAsBd;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHowItWorksSection.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\n\ninterface HowItWorksContent {\n  title?: string;\n  description?: string;\n  step_1_title?: string;\n  step_1_description?: string;\n  step_1_icon?: string;\n  step_2_title?: string;\n  step_2_description?: string;\n  step_2_icon?: string;\n  step_3_title?: string;\n  step_3_description?: string;\n  step_3_icon?: string;\n  step_4_title?: string;\n  step_4_description?: string;\n  step_4_icon?: string;\n}\n\ninterface DynamicHowItWorksSectionProps {\n  content: HowItWorksContent;\n}\n\nconst DynamicHowItWorksSection = ({ content }: DynamicHowItWorksSectionProps) => {\n  const defaultContent = {\n    title: \"How It Works\",\n    description: \"Our streamlined process ensures quick implementation and immediate results.\",\n    step_1_title: \"Analysis\",\n    step_1_description: \"We analyze your current ad setup and identify optimization opportunities.\",\n    step_2_title: \"Strategy\",\n    step_2_description: \"Develop a customized optimization strategy based on your specific needs.\",\n    step_3_title: \"Implementation\",\n    step_3_description: \"Deploy our solutions with minimal disruption to your current operations.\",\n    step_4_title: \"Optimization\",\n    step_4_description: \"Continuous monitoring and optimization to maximize your revenue potential.\"\n  };\n\n  const sectionData = { ...defaultContent, ...content };\n\n  const steps = [\n    {\n      number: \"01\",\n      title: sectionData.step_1_title,\n      description: sectionData.step_1_description,\n      icon: sectionData.step_1_icon\n    },\n    {\n      number: \"02\",\n      title: sectionData.step_2_title,\n      description: sectionData.step_2_description,\n      icon: sectionData.step_2_icon\n    },\n    {\n      number: \"03\",\n      title: sectionData.step_3_title,\n      description: sectionData.step_3_description,\n      icon: sectionData.step_3_icon\n    },\n    {\n      number: \"04\",\n      title: sectionData.step_4_title,\n      description: sectionData.step_4_description,\n      icon: sectionData.step_4_icon\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            {sectionData.title}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {sectionData.description}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {steps.map((step, index) => (\n            <div key={index} className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mb-6 mx-auto\">\n                {step.icon ? (\n                  <Image\n                    src={step.icon}\n                    alt={step.title}\n                    width={32}\n                    height={32}\n                    className=\"h-8 w-8 object-contain\"\n                  />\n                ) : (\n                  step.number\n                )}\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                {step.title}\n              </h3>\n              <p className=\"text-gray-600\">\n                {step.description}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default DynamicHowItWorksSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAyBA,MAAM,2BAA2B,CAAC,EAAE,OAAO,EAAiC;IAC1E,MAAM,iBAAiB;QACrB,OAAO;QACP,aAAa;QACb,cAAc;QACd,oBAAoB;QACpB,cAAc;QACd,oBAAoB;QACpB,cAAc;QACd,oBAAoB;QACpB,cAAc;QACd,oBAAoB;IACtB;IAEA,MAAM,cAAc;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IAEpD,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO,YAAY,YAAY;YAC/B,aAAa,YAAY,kBAAkB;YAC3C,MAAM,YAAY,WAAW;QAC/B;QACA;YACE,QAAQ;YACR,OAAO,YAAY,YAAY;YAC/B,aAAa,YAAY,kBAAkB;YAC3C,MAAM,YAAY,WAAW;QAC/B;QACA;YACE,QAAQ;YACR,OAAO,YAAY,YAAY;YAC/B,aAAa,YAAY,kBAAkB;YAC3C,MAAM,YAAY,WAAW;QAC/B;QACA;YACE,QAAQ;YACR,OAAO,YAAY,YAAY;YAC/B,aAAa,YAAY,kBAAkB;YAC3C,MAAM,YAAY,WAAW;QAC/B;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,YAAY,KAAK;;;;;;sCAEpB,8OAAC;4BAAE,WAAU;sCACV,YAAY,WAAW;;;;;;;;;;;;8BAI5B,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,iBACR,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,KAAK,IAAI;wCACd,KAAK,KAAK,KAAK;wCACf,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;mFAGZ,KAAK,MAAM;;;;;;8CAGf,8OAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;2BAlBX;;;;;;;;;;;;;;;;;;;;;AA0BtB;uCAEe", "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicExpertiseSection.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\n\ninterface ExpertiseContent {\n  title?: string;\n  description?: string;\n  expertise_1_title?: string;\n  expertise_1_description?: string;\n  expertise_1_icon?: string;\n  expertise_2_title?: string;\n  expertise_2_description?: string;\n  expertise_2_icon?: string;\n  expertise_3_title?: string;\n  expertise_3_description?: string;\n  expertise_3_icon?: string;\n}\n\ninterface DynamicExpertiseSectionProps {\n  content: ExpertiseContent;\n}\n\nconst DynamicExpertiseSection = ({ content }: DynamicExpertiseSectionProps) => {\n  const defaultContent = {\n    title: \"Our Expertise\",\n    description: \"Deep knowledge across all major ad platforms and technologies.\",\n    expertise_1_title: \"Programmatic Advertising\",\n    expertise_1_description: \"Advanced programmatic strategies and real-time bidding optimization.\",\n    expertise_2_title: \"Header Bidding\",\n    expertise_2_description: \"Implementation and optimization of header bidding solutions.\",\n    expertise_3_title: \"Ad Quality & Fraud Prevention\",\n    expertise_3_description: \"Comprehensive ad quality control and fraud detection systems.\"\n  };\n\n  const sectionData = { ...defaultContent, ...content };\n\n  const expertiseAreas = [\n    {\n      title: sectionData.expertise_1_title,\n      description: sectionData.expertise_1_description,\n      icon: sectionData.expertise_1_icon\n    },\n    {\n      title: sectionData.expertise_2_title,\n      description: sectionData.expertise_2_description,\n      icon: sectionData.expertise_2_icon\n    },\n    {\n      title: sectionData.expertise_3_title,\n      description: sectionData.expertise_3_description,\n      icon: sectionData.expertise_3_icon\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            {sectionData.title}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {sectionData.description}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {expertiseAreas.map((area, index) => (\n            <div key={index} className=\"bg-gray-50 rounded-xl p-8 text-center\">\n              {area.icon && (\n                <div className=\"w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mb-6 mx-auto\">\n                  <Image\n                    src={area.icon}\n                    alt={area.title}\n                    width={32}\n                    height={32}\n                    className=\"h-8 w-8 object-contain\"\n                  />\n                </div>\n              )}\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                {area.title}\n              </h3>\n              <p className=\"text-gray-600\">\n                {area.description}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default DynamicExpertiseSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,0BAA0B,CAAC,EAAE,OAAO,EAAgC;IACxE,MAAM,iBAAiB;QACrB,OAAO;QACP,aAAa;QACb,mBAAmB;QACnB,yBAAyB;QACzB,mBAAmB;QACnB,yBAAyB;QACzB,mBAAmB;QACnB,yBAAyB;IAC3B;IAEA,MAAM,cAAc;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IAEpD,MAAM,iBAAiB;QACrB;YACE,OAAO,YAAY,iBAAiB;YACpC,aAAa,YAAY,uBAAuB;YAChD,MAAM,YAAY,gBAAgB;QACpC;QACA;YACE,OAAO,YAAY,iBAAiB;YACpC,aAAa,YAAY,uBAAuB;YAChD,MAAM,YAAY,gBAAgB;QACpC;QACA;YACE,OAAO,YAAY,iBAAiB;YACpC,aAAa,YAAY,uBAAuB;YAChD,MAAM,YAAY,gBAAgB;QACpC;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,YAAY,KAAK;;;;;;sCAEpB,8OAAC;4BAAE,WAAU;sCACV,YAAY,WAAW;;;;;;;;;;;;8BAI5B,8OAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;4BAAgB,WAAU;;gCACxB,KAAK,IAAI,kBACR,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,KAAK,IAAI;wCACd,KAAK,KAAK,KAAK;wCACf,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAIhB,8OAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;2BAhBX;;;;;;;;;;;;;;;;;;;;;AAwBtB;uCAEe", "debugId": null}}, {"offset": {"line": 1177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicTestimonialsSection.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\n\ninterface TestimonialsContent {\n  title?: string;\n  description?: string;\n  testimonial_1_text?: string;\n  testimonial_1_author?: string;\n  testimonial_1_company?: string;\n  testimonial_1_logo?: string;\n  testimonial_1_avatar?: string;\n  testimonial_2_text?: string;\n  testimonial_2_author?: string;\n  testimonial_2_company?: string;\n  testimonial_2_logo?: string;\n  testimonial_2_avatar?: string;\n  testimonial_3_text?: string;\n  testimonial_3_author?: string;\n  testimonial_3_company?: string;\n  testimonial_3_logo?: string;\n  testimonial_3_avatar?: string;\n}\n\ninterface DynamicTestimonialsSectionProps {\n  content: TestimonialsContent;\n}\n\nconst DynamicTestimonialsSection = ({ content }: DynamicTestimonialsSectionProps) => {\n  const defaultContent = {\n    title: \"What Our Clients Say\",\n    description: \"Hear from publishers who have transformed their ad revenue with RevAdOps.\",\n    testimonial_1_text: \"RevAdOps increased our ad revenue by 45% in just 3 months. Their team is incredibly knowledgeable and responsive.\",\n    testimonial_1_author: \"<PERSON>\",\n    testimonial_1_company: \"TechNews Daily\",\n    testimonial_2_text: \"The fraud detection capabilities saved us thousands in invalid traffic. Highly recommend their services.\",\n    testimonial_2_author: \"Mike Chen\",\n    testimonial_2_company: \"Gaming Hub\",\n    testimonial_3_text: \"Professional service and outstanding results. Our fill rates improved dramatically.\",\n    testimonial_3_author: \"Lisa Rodriguez\",\n    testimonial_3_company: \"Mobile App Co.\"\n  };\n\n  const sectionData = { ...defaultContent, ...content };\n\n  const testimonials = [\n    {\n      text: sectionData.testimonial_1_text,\n      author: sectionData.testimonial_1_author,\n      company: sectionData.testimonial_1_company,\n      logo: sectionData.testimonial_1_logo,\n      avatar: sectionData.testimonial_1_avatar\n    },\n    {\n      text: sectionData.testimonial_2_text,\n      author: sectionData.testimonial_2_author,\n      company: sectionData.testimonial_2_company,\n      logo: sectionData.testimonial_2_logo,\n      avatar: sectionData.testimonial_2_avatar\n    },\n    {\n      text: sectionData.testimonial_3_text,\n      author: sectionData.testimonial_3_author,\n      company: sectionData.testimonial_3_company,\n      logo: sectionData.testimonial_3_logo,\n      avatar: sectionData.testimonial_3_avatar\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            {sectionData.title}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {sectionData.description}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {testimonials.map((testimonial, index) => (\n            <div key={index} className=\"bg-white rounded-xl p-8 shadow-lg\">\n              {testimonial.logo && (\n                <div className=\"mb-4\">\n                  <Image\n                    src={testimonial.logo}\n                    alt={`${testimonial.company} logo`}\n                    width={120}\n                    height={32}\n                    className=\"h-8 object-contain\"\n                  />\n                </div>\n              )}\n              <p className=\"text-gray-600 mb-6 italic\">\n                &ldquo;{testimonial.text}&rdquo;\n              </p>\n              <div className=\"flex items-center\">\n                {testimonial.avatar && (\n                  <Image\n                    src={testimonial.avatar}\n                    alt={testimonial.author}\n                    width={48}\n                    height={48}\n                    className=\"w-12 h-12 rounded-full object-cover mr-4\"\n                  />\n                )}\n                <div>\n                  <p className=\"font-semibold text-gray-900\">{testimonial.author}</p>\n                  <p className=\"text-gray-500\">{testimonial.company}</p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default DynamicTestimonialsSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA4BA,MAAM,6BAA6B,CAAC,EAAE,OAAO,EAAmC;IAC9E,MAAM,iBAAiB;QACrB,OAAO;QACP,aAAa;QACb,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;IACzB;IAEA,MAAM,cAAc;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IAEpD,MAAM,eAAe;QACnB;YACE,MAAM,YAAY,kBAAkB;YACpC,QAAQ,YAAY,oBAAoB;YACxC,SAAS,YAAY,qBAAqB;YAC1C,MAAM,YAAY,kBAAkB;YACpC,QAAQ,YAAY,oBAAoB;QAC1C;QACA;YACE,MAAM,YAAY,kBAAkB;YACpC,QAAQ,YAAY,oBAAoB;YACxC,SAAS,YAAY,qBAAqB;YAC1C,MAAM,YAAY,kBAAkB;YACpC,QAAQ,YAAY,oBAAoB;QAC1C;QACA;YACE,MAAM,YAAY,kBAAkB;YACpC,QAAQ,YAAY,oBAAoB;YACxC,SAAS,YAAY,qBAAqB;YAC1C,MAAM,YAAY,kBAAkB;YACpC,QAAQ,YAAY,oBAAoB;QAC1C;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,YAAY,KAAK;;;;;;sCAEpB,8OAAC;4BAAE,WAAU;sCACV,YAAY,WAAW;;;;;;;;;;;;8BAI5B,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;4BAAgB,WAAU;;gCACxB,YAAY,IAAI,kBACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,YAAY,IAAI;wCACrB,KAAK,GAAG,YAAY,OAAO,CAAC,KAAK,CAAC;wCAClC,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAIhB,8OAAC;oCAAE,WAAU;;wCAA4B;wCAC/B,YAAY,IAAI;wCAAC;;;;;;;8CAE3B,8OAAC;oCAAI,WAAU;;wCACZ,YAAY,MAAM,kBACjB,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,YAAY,MAAM;4CACvB,KAAK,YAAY,MAAM;4CACvB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA+B,YAAY,MAAM;;;;;;8DAC9D,8OAAC;oDAAE,WAAU;8DAAiB,YAAY,OAAO;;;;;;;;;;;;;;;;;;;2BA3B7C;;;;;;;;;;;;;;;;;;;;;AAoCtB;uCAEe", "debugId": null}}, {"offset": {"line": 1363, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicPartnersSection.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\n\ninterface PartnersContent {\n  title?: string;\n  description?: string;\n  partner_1_logo?: string;\n  partner_1_name?: string;\n  partner_2_logo?: string;\n  partner_2_name?: string;\n  partner_3_logo?: string;\n  partner_3_name?: string;\n  partner_4_logo?: string;\n  partner_4_name?: string;\n  partner_5_logo?: string;\n  partner_5_name?: string;\n  partner_6_logo?: string;\n  partner_6_name?: string;\n}\n\ninterface DynamicPartnersSectionProps {\n  content: PartnersContent;\n}\n\nconst DynamicPartnersSection = ({ content }: DynamicPartnersSectionProps) => {\n  const defaultContent = {\n    title: \"Trusted by Leading Publishers\",\n    description: \"Join hundreds of successful publishers who trust RevAdOps for their ad revenue optimization.\",\n    partner_1_name: \"Partner 1\",\n    partner_2_name: \"Partner 2\",\n    partner_3_name: \"Partner 3\",\n    partner_4_name: \"Partner 4\",\n    partner_5_name: \"Partner 5\",\n    partner_6_name: \"Partner 6\"\n  };\n\n  const sectionData = { ...defaultContent, ...content };\n\n  const partners = [\n    { logo: sectionData.partner_1_logo, name: sectionData.partner_1_name },\n    { logo: sectionData.partner_2_logo, name: sectionData.partner_2_name },\n    { logo: sectionData.partner_3_logo, name: sectionData.partner_3_name },\n    { logo: sectionData.partner_4_logo, name: sectionData.partner_4_name },\n    { logo: sectionData.partner_5_logo, name: sectionData.partner_5_name },\n    { logo: sectionData.partner_6_logo, name: sectionData.partner_6_name }\n  ].filter(partner => partner.logo); // Only show partners with logos\n\n  // Don't render the section if no partners have logos\n  if (partners.length === 0) {\n    return null;\n  }\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            {sectionData.title}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {sectionData.description}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center\">\n          {partners.map((partner, index) => (\n            <div key={index} className=\"flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n              {partner.logo && (\n                <Image\n                  src={partner.logo}\n                  alt={partner.name}\n                  width={120}\n                  height={48}\n                  className=\"max-h-12 w-auto object-contain grayscale hover:grayscale-0 transition-all\"\n                />\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default DynamicPartnersSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAyBA,MAAM,yBAAyB,CAAC,EAAE,OAAO,EAA+B;IACtE,MAAM,iBAAiB;QACrB,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,cAAc;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IAEpD,MAAM,WAAW;QACf;YAAE,MAAM,YAAY,cAAc;YAAE,MAAM,YAAY,cAAc;QAAC;QACrE;YAAE,MAAM,YAAY,cAAc;YAAE,MAAM,YAAY,cAAc;QAAC;QACrE;YAAE,MAAM,YAAY,cAAc;YAAE,MAAM,YAAY,cAAc;QAAC;QACrE;YAAE,MAAM,YAAY,cAAc;YAAE,MAAM,YAAY,cAAc;QAAC;QACrE;YAAE,MAAM,YAAY,cAAc;YAAE,MAAM,YAAY,cAAc;QAAC;QACrE;YAAE,MAAM,YAAY,cAAc;YAAE,MAAM,YAAY,cAAc;QAAC;KACtE,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,IAAI,GAAG,gCAAgC;IAEnE,qDAAqD;IACrD,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,YAAY,KAAK;;;;;;sCAEpB,8OAAC;4BAAE,WAAU;sCACV,YAAY,WAAW;;;;;;;;;;;;8BAI5B,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAAgB,WAAU;sCACxB,QAAQ,IAAI,kBACX,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,IAAI;gCACjB,KAAK,QAAQ,IAAI;gCACjB,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;2BAPN;;;;;;;;;;;;;;;;;;;;;AAgBtB;uCAEe", "debugId": null}}, {"offset": {"line": 1488, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicFinalCTASection.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { ArrowRight } from 'lucide-react';\n\ninterface FinalCTAContent {\n  title?: string;\n  description?: string;\n  cta_primary_text?: string;\n  cta_primary_link?: string;\n  cta_secondary_text?: string;\n  cta_secondary_link?: string;\n}\n\ninterface DynamicFinalCTASectionProps {\n  content: FinalCTAContent;\n}\n\nconst DynamicFinalCTASection = ({ content }: DynamicFinalCTASectionProps) => {\n  const defaultContent = {\n    title: \"Ready to Maximize Your Ad Revenue?\",\n    description: \"Join hundreds of publishers who have increased their revenue with RevAdOps. Get started with a free consultation today.\",\n    cta_primary_text: \"Get Free Consultation\",\n    cta_primary_link: \"/consultation\",\n    cta_secondary_text: \"Contact Us\",\n    cta_secondary_link: \"/contact\"\n  };\n\n  const sectionData = { ...defaultContent, ...content };\n\n  return (\n    <section className=\"py-20 bg-blue-600\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n          {sectionData.title}\n        </h2>\n        <p className=\"text-xl text-blue-100 mb-8 max-w-3xl mx-auto\">\n          {sectionData.description}\n        </p>\n        \n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Link\n            href={sectionData.cta_primary_link || '/consultation'}\n            className=\"inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200 shadow-lg\"\n          >\n            {sectionData.cta_primary_text}\n            <ArrowRight className=\"ml-2 h-5 w-5\" />\n          </Link>\n          \n          {sectionData.cta_secondary_text && (\n            <Link\n              href={sectionData.cta_secondary_link || '/contact'}\n              className=\"inline-flex items-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-all duration-200\"\n            >\n              {sectionData.cta_secondary_text}\n            </Link>\n          )}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default DynamicFinalCTASection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAkBA,MAAM,yBAAyB,CAAC,EAAE,OAAO,EAA+B;IACtE,MAAM,iBAAiB;QACrB,OAAO;QACP,aAAa;QACb,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,oBAAoB;IACtB;IAEA,MAAM,cAAc;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IAEpD,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BACX,YAAY,KAAK;;;;;;8BAEpB,8OAAC;oBAAE,WAAU;8BACV,YAAY,WAAW;;;;;;8BAG1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,YAAY,gBAAgB,IAAI;4BACtC,WAAU;;gCAET,YAAY,gBAAgB;8CAC7B,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;wBAGvB,YAAY,kBAAkB,kBAC7B,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,YAAY,kBAAkB,IAAI;4BACxC,WAAU;sCAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAO7C;uCAEe", "debugId": null}}, {"offset": {"line": 1585, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport DynamicHeroSection from '@/components/DynamicHeroSection';\nimport DynamicWhatWeDoSection from '@/components/DynamicWhatWeDoSection';\nimport DynamicWhyChooseSection from '@/components/DynamicWhyChooseSection';\nimport DynamicHowItWorksSection from '@/components/DynamicHowItWorksSection';\nimport DynamicExpertiseSection from '@/components/DynamicExpertiseSection';\nimport DynamicTestimonialsSection from '@/components/DynamicTestimonialsSection';\nimport DynamicPartnersSection from '@/components/DynamicPartnersSection';\nimport DynamicFinalCTASection from '@/components/DynamicFinalCTASection';\n\ninterface ContentSection {\n  [key: string]: string;\n}\n\ninterface ContentData {\n  hero?: ContentSection;\n  what_we_do?: ContentSection;\n  why_choose_us?: ContentSection;\n  how_it_works?: ContentSection;\n  our_expertise?: ContentSection;\n  testimonials?: ContentSection;\n  partners?: ContentSection;\n  final_cta?: ContentSection;\n  [key: string]: ContentSection | undefined;\n}\n\nexport default function Home() {\n  const [content, setContent] = useState<ContentData>({});\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    fetchContent();\n  }, []);\n\n  const fetchContent = async () => {\n    try {\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/content`);\n      if (response.ok) {\n        const data = await response.json();\n        setContent(data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch content:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n\n\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main>\n        <DynamicHeroSection content={content.hero || {}} />\n        <DynamicWhatWeDoSection content={content.what_we_do || {}} />\n        <DynamicWhyChooseSection content={content.why_choose_us || {}} />\n        <DynamicHowItWorksSection content={content.how_it_works || {}} />\n        <DynamicExpertiseSection content={content.our_expertise || {}} />\n        <DynamicTestimonialsSection content={content.testimonials || {}} />\n        <DynamicPartnersSection content={content.partners || {}} />\n        <DynamicFinalCTASection content={content.final_cta || {}} />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AA8Be,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,CAAC;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iEAAmC,QAAQ,CAAC;YACzE,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAIA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCACC,8OAAC,wIAAA,CAAA,UAAkB;wBAAC,SAAS,QAAQ,IAAI,IAAI,CAAC;;;;;;kCAC9C,8OAAC,4IAAA,CAAA,UAAsB;wBAAC,SAAS,QAAQ,UAAU,IAAI,CAAC;;;;;;kCACxD,8OAAC,6IAAA,CAAA,UAAuB;wBAAC,SAAS,QAAQ,aAAa,IAAI,CAAC;;;;;;kCAC5D,8OAAC,8IAAA,CAAA,UAAwB;wBAAC,SAAS,QAAQ,YAAY,IAAI,CAAC;;;;;;kCAC5D,8OAAC,6IAAA,CAAA,UAAuB;wBAAC,SAAS,QAAQ,aAAa,IAAI,CAAC;;;;;;kCAC5D,8OAAC,gJAAA,CAAA,UAA0B;wBAAC,SAAS,QAAQ,YAAY,IAAI,CAAC;;;;;;kCAC9D,8OAAC,4IAAA,CAAA,UAAsB;wBAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC;;;;;;kCACtD,8OAAC,4IAAA,CAAA,UAAsB;wBAAC,SAAS,QAAQ,SAAS,IAAI,CAAC;;;;;;;;;;;;0BAEzD,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}