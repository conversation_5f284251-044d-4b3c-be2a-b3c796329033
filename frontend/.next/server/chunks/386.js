exports.id=386,exports.ids=[386],exports.modules={9005:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},16189:(a,b,c)=>{"use strict";var d=c(65773);c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},25472:(a,b,c)=>{Promise.resolve().then(c.bind(c,95848))},33748:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},40476:()=>{},54044:()=>{},57716:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},61135:()=>{},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},78272:(a,b,c)=>{Promise.resolve().then(c.bind(c,99111))},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g,metadata:()=>f,viewport:()=>e});var d=c(37413);c(61135);let e={width:"device-width",initialScale:1},f={title:"RevAdOps - Unlock Your Ad Revenue Potential with Intelligent Ad Operations",description:"RevAdOps helps publishers and app developers maximize revenue, improve fill rates, and maintain healthy traffic quality through advanced AdTech solutions and data-driven optimization.",keywords:"ad revenue, programmatic advertising, header bidding, ad monetization, publishers, app developers, adtech",authors:[{name:"RevAdOps Team"}],robots:"index, follow",icons:{icon:[{url:"/favicon.png",sizes:"32x32",type:"image/png"},{url:"/revadops-logo.png",sizes:"192x192",type:"image/png"}],apple:[{url:"/revadops-logo.png",sizes:"180x180",type:"image/png"}]}};function g({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{children:a})})}},95848:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s});var d=c(60687),e=c(43210),f=c(16189),g=c(85814),h=c.n(g),i=c(62688);let j=(0,i.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var k=c(10022),l=c(9005),m=c(41312),n=c(53411);let o=(0,i.A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var p=c(11860);let q=(0,i.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var r=c(12941);function s({children:a}){let[b,c]=(0,e.useState)(null),[g,i]=(0,e.useState)(!0),[s,t]=(0,e.useState)(!1),u=(0,f.useRouter)(),v=(0,f.usePathname)(),w=[{name:"Dashboard",href:"/admin/dashboard",icon:j},{name:"Homepage Content",href:"/admin/homepage",icon:k.A},{name:"Media Library",href:"/admin/media",icon:l.A},{name:"Leads",href:"/admin/leads",icon:m.A},{name:"Analytics",href:"/admin/analytics",icon:n.A},{name:"Settings",href:"/admin/settings",icon:o}];return"/admin/login"===v?a:g?(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):b?(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[s&&(0,d.jsx)("div",{className:"fixed inset-0 z-40 lg:hidden",children:(0,d.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>t(!1)})}),(0,d.jsxs)("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${s?"translate-x-0":"-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:flex lg:flex-col`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,d.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RevAdOps Admin"}),(0,d.jsx)("button",{onClick:()=>t(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,d.jsx)(p.A,{className:"h-6 w-6"})})]}),(0,d.jsx)("nav",{className:"mt-6 px-3",children:(0,d.jsx)("div",{className:"space-y-1",children:w.map(a=>{let b=a.icon,c=v===a.href;return(0,d.jsxs)(h(),{href:a.href,className:`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${c?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>t(!1),children:[(0,d.jsx)(b,{className:`mr-3 h-5 w-5 ${c?"text-blue-500":"text-gray-400 group-hover:text-gray-500"}`}),a.name]},a.name)})})}),(0,d.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200",children:[(0,d.jsx)("div",{className:"flex items-center mb-3",children:(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:b.name}),(0,d.jsx)("p",{className:"text-xs text-gray-500 truncate",children:b.email})]})}),(0,d.jsxs)("button",{onClick:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),u.push("/admin/login")},className:"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200",children:[(0,d.jsx)(q,{className:"mr-3 h-5 w-5 text-gray-400"}),"Sign out"]})]})]}),(0,d.jsxs)("div",{className:"lg:pl-64 flex-1 flex flex-col min-h-screen",children:[(0,d.jsx)("div",{className:"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,d.jsx)("button",{onClick:()=>t(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,d.jsx)(r.A,{className:"h-6 w-6"})}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:["Welcome back, ",b.name]})})]})}),(0,d.jsx)("main",{className:"flex-1 p-6 overflow-auto",children:a})]})]}):null}},99111:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/layout.tsx","default")}};