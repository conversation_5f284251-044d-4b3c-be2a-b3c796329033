(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[93],{398:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(5155),r=t(2115),l=t(2657),c=t(7580),i=t(4186),d=t(3109);let n=(0,t(9946).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);var o=t(6767),m=t(4869),h=t(9074);function x(){let[e,s]=(0,r.useState)(null),[t,x]=(0,r.useState)(!0),[g,u]=(0,r.useState)("7d");(0,r.useEffect)(()=>{p()},[g]);let p=async()=>{try{s({pageViews:12547,uniqueVisitors:8932,avgSessionDuration:"2m 34s",bounceRate:42.3,topPages:[{page:"/",views:5234},{page:"/services",views:2156},{page:"/blog",views:1876},{page:"/contact",views:1432},{page:"/about",views:987}],deviceBreakdown:[{device:"Desktop",percentage:58.2},{device:"Mobile",percentage:35.7},{device:"Tablet",percentage:6.1}],trafficSources:[{source:"Organic Search",percentage:45.2},{source:"Direct",percentage:28.7},{source:"Social Media",percentage:15.3},{source:"Referral",percentage:10.8}]})}catch(e){console.error("Failed to fetch analytics:",e)}finally{x(!1)}};return t?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Analytics"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Website performance and visitor insights"})]}),(0,a.jsxs)("select",{value:g,onChange:e=>u(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white",children:[(0,a.jsx)("option",{value:"7d",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30d",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90d",children:"Last 90 days"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{label:"Page Views",value:e.pageViews.toLocaleString(),icon:l.A,color:"bg-blue-500",change:"+12.5%"},{label:"Unique Visitors",value:e.uniqueVisitors.toLocaleString(),icon:c.A,color:"bg-green-500",change:"+8.2%"},{label:"Avg. Session Duration",value:e.avgSessionDuration,icon:i.A,color:"bg-purple-500",change:"+5.7%"},{label:"Bounce Rate",value:"".concat(e.bounceRate,"%"),icon:d.A,color:"bg-orange-500",change:"-3.1%"}].map((e,s)=>{let t=e.icon;return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value}),(0,a.jsx)("p",{className:"text-sm text-green-600",children:e.change})]}),(0,a.jsx)("div",{className:"p-3 rounded-full ".concat(e.color),children:(0,a.jsx)(t,{className:"h-6 w-6 text-white"})})]})},s)})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Top Pages"}),(0,a.jsx)("div",{className:"space-y-4",children:e.topPages.map((s,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:s.page})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(s.views/e.topPages[0].views*100,"%")}})}),(0,a.jsx)("span",{className:"text-sm text-gray-600 w-16 text-right",children:s.views.toLocaleString()})]})]},t))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Device Breakdown"}),(0,a.jsx)("div",{className:"space-y-4",children:e.deviceBreakdown.map((e,s)=>{let t={Desktop:n,Mobile:o.A,Tablet:o.A}[e.device];return(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(t,{className:"h-5 w-5 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.device})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(e.percentage,"%")}})}),(0,a.jsxs)("span",{className:"text-sm text-gray-600 w-12 text-right",children:[e.percentage,"%"]})]})]},s)})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Traffic Sources"}),(0,a.jsx)("div",{className:"space-y-4",children:e.trafficSources.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.source})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-purple-600 h-2 rounded-full",style:{width:"".concat(e.percentage,"%")}})}),(0,a.jsxs)("span",{className:"text-sm text-gray-600 w-12 text-right",children:[e.percentage,"%"]})]})]},s))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Activity"}),(0,a.jsx)("div",{className:"space-y-4",children:[{action:"Homepage content updated",time:"2 hours ago"},{action:"New lead from contact form",time:"4 hours ago"},{action:"Blog post published",time:"1 day ago"},{action:"Media uploaded to library",time:"2 days ago"},{action:"Services page updated",time:"3 days ago"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:e.action})]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.time})]},s))})]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-yellow-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Analytics Integration"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"Connect Google Analytics or other analytics services for real-time data. Current data is simulated for demonstration purposes."})]})]})})]}):(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-600",children:"Failed to load analytics data"})})}},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3109:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},6767:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>d});var a=t(2115);let r=e=>{let s=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase());return s.charAt(0).toUpperCase()+s.slice(1)},l=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:n="",children:o,iconNode:m,...h}=e;return(0,a.createElement)("svg",{ref:s,...c,width:r,height:r,stroke:t,strokeWidth:d?24*Number(i)/Number(r):i,className:l("lucide",n),...!o&&!(e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0})(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(o)?o:[o]])}),d=(e,s)=>{let t=(0,a.forwardRef)((t,c)=>{let{className:d,...n}=t;return(0,a.createElement)(i,{ref:c,iconNode:s,className:l("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...n})});return t.displayName=r(e),t}},9994:(e,s,t)=>{Promise.resolve().then(t.bind(t,398))}},e=>{e.O(0,[441,964,358],()=>e(e.s=9994)),_N_E=e.O()}]);