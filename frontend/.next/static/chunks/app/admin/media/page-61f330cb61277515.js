(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[709],{2525:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},5746:(e,t,a)=>{Promise.resolve().then(a.bind(a,7150))},7150:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(5155),r=a(2115),l=a(6766),i=a(9946);let d=(0,i.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var o=a(7924);let c=(0,i.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),n=(0,i.A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var h=a(7213),m=a(9803),x=a(2525);function u(){let[e,t]=(0,r.useState)([]),[a,i]=(0,r.useState)(!0),[u,g]=(0,r.useState)(!1),[p,y]=(0,r.useState)(""),[f,j]=(0,r.useState)("all"),[b,v]=(0,r.useState)("grid");(0,r.useEffect)(()=>{N()},[]);let N=async()=>{try{let e=localStorage.getItem("adminToken"),a=await fetch("".concat("http://localhost:5001/api","/upload/admin/assets"),{headers:{Authorization:"Bearer ".concat(e)}});if(a.ok){let e=await a.json();t(e.assets||[])}}catch(e){console.error("Failed to fetch assets:",e)}finally{i(!1)}},w=async e=>{g(!0);try{let a=localStorage.getItem("adminToken"),s=new FormData;s.append("image",e),s.append("maxWidth","2000"),s.append("maxHeight","2000");let r=await fetch("".concat("http://localhost:5001/api","/upload/admin/image"),{method:"POST",headers:{Authorization:"Bearer ".concat(a)},body:s});if(r.ok){let e=await r.json();t(t=>[e.asset,...t]),alert("Image uploaded successfully!")}else alert("Failed to upload image")}catch(e){console.error("Upload error:",e),alert("Failed to upload image")}finally{g(!1)}},k=async e=>{if(confirm("Are you sure you want to delete this asset?"))try{let a=localStorage.getItem("adminToken");(await fetch("".concat("http://localhost:5001/api","/upload/admin/assets/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(a)}})).ok?(t(t=>t.filter(t=>t.id!==e)),alert("Asset deleted successfully!")):alert("Failed to delete asset")}catch(e){console.error("Delete error:",e),alert("Failed to delete asset")}},M=e.filter(e=>{let t=e.originalName.toLowerCase().includes(p.toLowerCase()),a="all"===f||"image"===f&&e.mimeType.startsWith("image/")||"video"===f&&e.mimeType.startsWith("video/");return t&&a}),A=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return a?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Media Library"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage your images and videos"})]}),(0,s.jsxs)("label",{className:"cursor-pointer inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[(0,s.jsx)(d,{className:"h-4 w-4 mr-2"}),u?"Uploading...":"Upload Media",(0,s.jsx)("input",{type:"file",accept:"image/*",className:"hidden",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];a&&w(a)},disabled:u})]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search media...",value:p,onChange:e=>y(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white"})]}),(0,s.jsxs)("select",{value:f,onChange:e=>j(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",children:[(0,s.jsx)("option",{value:"all",children:"All Types"}),(0,s.jsx)("option",{value:"image",children:"Images"}),(0,s.jsx)("option",{value:"video",children:"Videos"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>v("grid"),className:"p-2 rounded-md ".concat("grid"===b?"bg-blue-100 text-blue-600":"text-gray-400 hover:text-gray-600"),children:(0,s.jsx)(c,{className:"h-4 w-4"})}),(0,s.jsx)("button",{onClick:()=>v("list"),className:"p-2 rounded-md ".concat("list"===b?"bg-blue-100 text-blue-600":"text-gray-400 hover:text-gray-600"),children:(0,s.jsx)(n,{className:"h-4 w-4"})})]})]})}),0===M.length?(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,s.jsx)(h.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No media found"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Upload your first image or video to get started."})]}):(0,s.jsx)("div",{className:"grid"===b?"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6":"space-y-4",children:M.map(e=>(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 ".concat("list"===b?"flex items-center p-4":""),children:"grid"===b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"aspect-square bg-gray-100 flex items-center justify-center",children:e.mimeType.startsWith("image/")?(0,s.jsx)(l.default,{src:e.cloudinaryUrl,alt:e.altText||e.originalName,fill:!0,className:"object-cover"}):(0,s.jsx)(m.A,{className:"h-12 w-12 text-gray-400"})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 truncate mb-1",children:e.originalName}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:A(e.size)}),e.width&&e.height&&(0,s.jsxs)("p",{className:"text-xs text-gray-400 mb-3",children:[e.width," \xd7 ",e.height]}),(0,s.jsxs)("button",{onClick:()=>k(e.id),className:"w-full flex items-center justify-center px-3 py-1 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"Delete"]})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-16 h-16 bg-gray-100 rounded-md flex items-center justify-center mr-4",children:e.mimeType.startsWith("image/")?(0,s.jsx)(l.default,{src:e.cloudinaryUrl,alt:e.altText||e.originalName,width:64,height:64,className:"w-full h-full object-cover rounded-md"}):(0,s.jsx)(m.A,{className:"h-8 w-8 text-gray-400"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 truncate",children:e.originalName}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:A(e.size)}),e.width&&e.height&&(0,s.jsxs)("p",{className:"text-xs text-gray-400",children:[e.width," \xd7 ",e.height]})]}),(0,s.jsx)("button",{onClick:()=>k(e.id),className:"flex-shrink-0 p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200",children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})},e.id))}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Upload Guidelines"}),(0,s.jsxs)("ul",{className:"text-xs text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"• Images: Maximum 5MB, recommended 2000x2000px or smaller"}),(0,s.jsx)("li",{children:"• Supported formats: JPG, PNG, GIF, WebP"}),(0,s.jsx)("li",{children:"• Images will be automatically optimized for web performance"}),(0,s.jsx)("li",{children:"• Use descriptive filenames for better organization"})]})]})]})}},7213:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7924:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9803:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])}},e=>{e.O(0,[651,441,964,358],()=>e(e.s=5746)),_N_E=e.O()}]);