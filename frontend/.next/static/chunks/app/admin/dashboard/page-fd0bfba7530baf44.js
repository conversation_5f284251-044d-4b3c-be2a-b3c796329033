(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{1154:(e,t,a)=>{Promise.resolve().then(a.bind(a,4163))},2657:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4163:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(5155),s=a(2115),i=a(7580),l=a(7434),d=a(7213),n=a(2657);function c(){let[e,t]=(0,s.useState)({totalLeads:0,totalContent:0,totalMedia:0,recentActivity:[]}),[a,c]=(0,s.useState)(!0);(0,s.useEffect)(()=>{o()},[]);let o=async()=>{try{t({totalLeads:45,totalContent:28,totalMedia:156,recentActivity:[{id:"1",type:"content",description:"Homepage hero section updated",timestamp:new Date().toISOString()},{id:"2",type:"lead",description:"New lead from contact form",timestamp:new Date(Date.now()-36e5).toISOString()},{id:"3",type:"media",description:"New image uploaded to media library",timestamp:new Date(Date.now()-72e5).toISOString()}]})}catch(e){console.error("Failed to fetch dashboard data:",e)}finally{c(!1)}},m=[{name:"Total Leads",value:e.totalLeads,icon:i.A,color:"bg-blue-500",href:"/admin/leads"},{name:"Content Pieces",value:e.totalContent,icon:l.A,color:"bg-green-500",href:"/admin/homepage"},{name:"Media Assets",value:e.totalMedia,icon:d.A,color:"bg-purple-500",href:"/admin/media"},{name:"Page Views",value:"12.5K",icon:n.A,color:"bg-orange-500",href:"/admin/analytics"}];return a?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Welcome to your RevAdOps admin panel"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:m.map(e=>{let t=e.icon;return(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.name}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value})]}),(0,r.jsx)("div",{className:"p-3 rounded-full ".concat(e.color),children:(0,r.jsx)(t,{className:"h-6 w-6 text-white"})})]})},e.name)})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("a",{href:"/admin/homepage",className:"flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200",children:[(0,r.jsx)(l.A,{className:"h-5 w-5 text-blue-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Edit Homepage"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Update homepage content and images"})]})]}),(0,r.jsxs)("a",{href:"/admin/media",className:"flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-green-500 mr-3","aria-label":"Upload Media"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Upload Media"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Add new images and videos"})]})]}),(0,r.jsxs)("a",{href:"/admin/leads",className:"flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200",children:[(0,r.jsx)(i.A,{className:"h-5 w-5 text-purple-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"View Leads"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Manage customer inquiries"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Activity"}),(0,r.jsx)("div",{className:"space-y-4",children:e.recentActivity.map(e=>(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsxs)("div",{className:"flex-shrink-0",children:["content"===e.type&&(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(l.A,{className:"h-4 w-4 text-blue-600"})}),"lead"===e.type&&(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"h-4 w-4 text-green-600"})}),"media"===e.type&&(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"h-4 w-4 text-purple-600","aria-label":"Media Activity"})})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm text-gray-900",children:e.description}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleString()})]})]},e.id))})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Website Preview"}),(0,r.jsxs)("a",{href:"/",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"View Live Site"]})]}),(0,r.jsx)("div",{className:"bg-gray-100 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-gray-600 text-center",children:'Preview your website changes here. Click "View Live Site" to see the current website.'})})]})]})}},7213:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7434:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7580:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9946:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(2115);let s=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:s=24,strokeWidth:d=2,absoluteStrokeWidth:n,className:c="",children:o,iconNode:m,...h}=e;return(0,r.createElement)("svg",{ref:t,...l,width:s,height:s,stroke:a,strokeWidth:n?24*Number(d)/Number(s):d,className:i("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(o)?o:[o]])}),n=(e,t)=>{let a=(0,r.forwardRef)((a,l)=>{let{className:n,...c}=a;return(0,r.createElement)(d,{ref:l,iconNode:t,className:i("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),n),...c})});return a.displayName=s(e),a}}},e=>{e.O(0,[441,964,358],()=>e(e.s=1154)),_N_E=e.O()}]);