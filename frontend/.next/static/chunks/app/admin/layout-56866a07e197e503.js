(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[581],{2920:(e,a,t)=>{Promise.resolve().then(t.bind(t,7018))},5695:(e,a,t)=>{"use strict";var s=t(8999);t.o(s,"usePathname")&&t.d(a,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(a,{useRouter:function(){return s.useRouter}})},7018:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>p});var s=t(5155),r=t(2115),n=t(5695),l=t(6874),i=t.n(l),o=t(9946);let d=(0,o.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var c=t(7434),m=t(7213),h=t(7580),x=t(2713);let u=(0,o.A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var g=t(4416);let f=(0,o.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var y=t(4783);function p(e){let{children:a}=e,[t,l]=(0,r.useState)(null),[o,p]=(0,r.useState)(!0),[b,v]=(0,r.useState)(!1),j=(0,n.useRouter)(),k=(0,n.usePathname)();(0,r.useEffect)(()=>{let e=async()=>{let e=localStorage.getItem("adminToken");if(!e)return void j.push("/admin/login");try{let a=await fetch("".concat("http://localhost:5001/api","/auth/verify"),{headers:{Authorization:"Bearer ".concat(e)}});if(a.ok){let e=await a.json();"admin"===e.user.role?l(e.user):(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),j.push("/admin/login"))}else localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),j.push("/admin/login")}catch(e){console.error("Auth check failed:",e),j.push("/admin/login")}finally{p(!1)}};"/admin/login"!==k?e():p(!1)},[j,k]);let N=[{name:"Dashboard",href:"/admin/dashboard",icon:d},{name:"Homepage Content",href:"/admin/homepage",icon:c.A},{name:"Media Library",href:"/admin/media",icon:m.A},{name:"Leads",href:"/admin/leads",icon:h.A},{name:"Analytics",href:"/admin/analytics",icon:x.A},{name:"Settings",href:"/admin/settings",icon:u}];return"/admin/login"===k?a:o?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):t?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[b&&(0,s.jsx)("div",{className:"fixed inset-0 z-40 lg:hidden",children:(0,s.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>v(!1)})}),(0,s.jsxs)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ".concat(b?"translate-x-0":"-translate-x-full"," transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:flex lg:flex-col"),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RevAdOps Admin"}),(0,s.jsx)("button",{onClick:()=>v(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,s.jsx)(g.A,{className:"h-6 w-6"})})]}),(0,s.jsx)("nav",{className:"mt-6 px-3",children:(0,s.jsx)("div",{className:"space-y-1",children:N.map(e=>{let a=e.icon,t=k===e.href;return(0,s.jsxs)(i(),{href:e.href,className:"group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ".concat(t?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"),onClick:()=>v(!1),children:[(0,s.jsx)(a,{className:"mr-3 h-5 w-5 ".concat(t?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})}),(0,s.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200",children:[(0,s.jsx)("div",{className:"flex items-center mb-3",children:(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:t.name}),(0,s.jsx)("p",{className:"text-xs text-gray-500 truncate",children:t.email})]})}),(0,s.jsxs)("button",{onClick:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),j.push("/admin/login")},className:"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200",children:[(0,s.jsx)(f,{className:"mr-3 h-5 w-5 text-gray-400"}),"Sign out"]})]})]}),(0,s.jsxs)("div",{className:"lg:pl-64 flex-1 flex flex-col min-h-screen",children:[(0,s.jsx)("div",{className:"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,s.jsx)("button",{onClick:()=>v(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,s.jsx)(y.A,{className:"h-6 w-6"})}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["Welcome back, ",t.name]})})]})}),(0,s.jsx)("main",{className:"flex-1 p-6 overflow-auto",children:a})]})]}):null}},7213:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7434:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])}},e=>{e.O(0,[810,441,964,358],()=>e(e.s=2920)),_N_E=e.O()}]);