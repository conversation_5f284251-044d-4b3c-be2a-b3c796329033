(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[574],{2013:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var s=a(5155),r=a(2115);let l=(0,a(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var i=a(8883),d=a(9420),n=a(8136),c=a(2525),o=a(2657);function m(){let[e,t]=(0,r.useState)([]),[a,m]=(0,r.useState)(!0),[h,x]=(0,r.useState)(null),[u,g]=(0,r.useState)("all");(0,r.useEffect)(()=>{p()},[]);let p=async()=>{try{let e=[{id:"1",name:"<PERSON>",email:"<EMAIL>",phone:"+****************",company:"TechCorp Inc.",message:"Interested in your ad optimization services for our mobile app.",source:"Contact Form",status:"new",createdAt:new Date().toISOString()},{id:"2",name:"Sarah Johnson",email:"<EMAIL>",phone:"+****************",company:"Publisher Co.",message:"Looking to improve our header bidding setup and increase CPMs.",source:"Homepage CTA",status:"contacted",createdAt:new Date(Date.now()-864e5).toISOString()},{id:"3",name:"Mike Chen",email:"<EMAIL>",company:"AdNetwork Solutions",message:"Want to discuss programmatic deals and revenue optimization.",source:"Blog",status:"qualified",createdAt:new Date(Date.now()-1728e5).toISOString()}];t(e)}catch(e){console.error("Failed to fetch leads:",e)}finally{m(!1)}},y=async(e,a)=>{try{t(t=>t.map(t=>t.id===e?{...t,status:a}:t))}catch(e){console.error("Failed to update lead status:",e)}},j=async e=>{if(confirm("Are you sure you want to delete this lead?"))try{t(t=>t.filter(t=>t.id!==e)),x(null)}catch(e){console.error("Failed to delete lead:",e)}},v=e.filter(e=>"all"===u||e.status===u);return a?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Leads Management"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage customer inquiries and leads"})]}),(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[(0,s.jsx)(l,{className:"h-4 w-4 mr-2"}),"Export CSV"]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[{label:"Total Leads",value:e.length,color:"bg-blue-500"},{label:"New",value:e.filter(e=>"new"===e.status).length,color:"bg-blue-500"},{label:"Contacted",value:e.filter(e=>"contacted"===e.status).length,color:"bg-yellow-500"},{label:"Qualified",value:e.filter(e=>"qualified"===e.status).length,color:"bg-green-500"}].map((e,t)=>(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value})]}),(0,s.jsx)("div",{className:"p-3 rounded-full ".concat(e.color),children:(0,s.jsx)(i.A,{className:"h-6 w-6 text-white"})})]})},t))}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,s.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Leads"}),(0,s.jsxs)("select",{value:u,onChange:e=>g(e.target.value),className:"px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white",children:[(0,s.jsx)("option",{value:"all",children:"All Status"}),(0,s.jsx)("option",{value:"new",children:"New"}),(0,s.jsx)("option",{value:"contacted",children:"Contacted"}),(0,s.jsx)("option",{value:"qualified",children:"Qualified"}),(0,s.jsx)("option",{value:"closed",children:"Closed"})]})]})}),(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:v.map(e=>(0,s.jsx)("div",{className:"p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-200 ".concat((null==h?void 0:h.id)===e.id?"bg-blue-50 border-l-4 border-blue-500":""),onClick:()=>x(e),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"new":return"bg-blue-100 text-blue-800";case"contacted":return"bg-yellow-100 text-yellow-800";case"qualified":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(e.status)),children:e.status})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 truncate",children:e.email}),e.company&&(0,s.jsx)("p",{className:"text-xs text-gray-400 truncate",children:e.company})]}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:new Date(e.createdAt).toLocaleDateString()})]})},e.id))})]})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,s.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Lead Details"})}),h?(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:h.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Created ",new Date(h.createdAt).toLocaleDateString()]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:h.email})]}),h.phone&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:h.phone})]}),h.company&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:h.company})]})]}),h.message&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Message"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded-md",children:h.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Status"}),(0,s.jsxs)("select",{value:h.status,onChange:e=>y(h.id,e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white",children:[(0,s.jsx)("option",{value:"new",children:"New"}),(0,s.jsx)("option",{value:"contacted",children:"Contacted"}),(0,s.jsx)("option",{value:"qualified",children:"Qualified"}),(0,s.jsx)("option",{value:"closed",children:"Closed"})]})]}),(0,s.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,s.jsxs)("button",{onClick:()=>j(h.id),className:"w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-200",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Delete Lead"]})})]}):(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)(o.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-2",children:"No lead selected"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Select a lead from the list to view details"})]})]})})]})]})}},2525:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},2657:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8136:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8883:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9715:(e,t,a)=>{Promise.resolve().then(a.bind(a,2013))},9946:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(2115);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:d=2,absoluteStrokeWidth:n,className:c="",children:o,iconNode:m,...h}=e;return(0,s.createElement)("svg",{ref:t,...i,width:r,height:r,stroke:a,strokeWidth:n?24*Number(d)/Number(r):d,className:l("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...Array.isArray(o)?o:[o]])}),n=(e,t)=>{let a=(0,s.forwardRef)((a,i)=>{let{className:n,...c}=a;return(0,s.createElement)(d,{ref:i,iconNode:t,className:l("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),n),...c})});return a.displayName=r(e),a}}},e=>{e.O(0,[441,964,358],()=>e(e.s=9715)),_N_E=e.O()}]);