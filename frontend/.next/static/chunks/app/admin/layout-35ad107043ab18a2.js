(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[581],{2920:(e,t,r)=>{Promise.resolve().then(r.bind(r,7018))},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let a=r(2115);function n(e,t){let r=(0,a.useRef)(null),n=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=r.current;e&&(r.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(r.current=l(e,a)),t&&(n.current=l(t,a))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7018:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(5155),n=r(2115),l=r(5695),s=r(6874),i=r.n(s),o=r(9946);let c=(0,o.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var d=r(7434),u=r(7213),m=r(7580),h=r(2713);let f=(0,o.A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var x=r(4416);let g=(0,o.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var y=r(4783);function p(e){let{children:t}=e,[r,s]=(0,n.useState)(null),[o,p]=(0,n.useState)(!0),[b,v]=(0,n.useState)(!1),j=(0,l.useRouter)(),k=(0,l.usePathname)();(0,n.useEffect)(()=>{let e=async()=>{let e=localStorage.getItem("adminToken");if(!e)return void j.push("/admin/login");try{let t=await fetch("".concat("http://localhost:5001/api","/auth/verify"),{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();"admin"===e.user.role?s(e.user):(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),j.push("/admin/login"))}else localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),j.push("/admin/login")}catch(e){console.error("Auth check failed:",e),j.push("/admin/login")}finally{p(!1)}};"/admin/login"!==k?e():p(!1)},[j,k]);let N=[{name:"Dashboard",href:"/admin/dashboard",icon:c},{name:"Homepage Content",href:"/admin/homepage",icon:d.A},{name:"Media Library",href:"/admin/media",icon:u.A},{name:"Leads",href:"/admin/leads",icon:m.A},{name:"Analytics",href:"/admin/analytics",icon:h.A},{name:"Settings",href:"/admin/settings",icon:f}];return"/admin/login"===k?t:o?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):r?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[b&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 lg:hidden",children:(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>v(!1)})}),(0,a.jsxs)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ".concat(b?"translate-x-0":"-translate-x-full"," transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:flex lg:flex-col"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RevAdOps Admin"}),(0,a.jsx)("button",{onClick:()=>v(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,a.jsx)(x.A,{className:"h-6 w-6"})})]}),(0,a.jsx)("nav",{className:"mt-6 px-3",children:(0,a.jsx)("div",{className:"space-y-1",children:N.map(e=>{let t=e.icon,r=k===e.href;return(0,a.jsxs)(i(),{href:e.href,className:"group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ".concat(r?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"),onClick:()=>v(!1),children:[(0,a.jsx)(t,{className:"mr-3 h-5 w-5 ".concat(r?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})}),(0,a.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200",children:[(0,a.jsx)("div",{className:"flex items-center mb-3",children:(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:r.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500 truncate",children:r.email})]})}),(0,a.jsxs)("button",{onClick:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),j.push("/admin/login")},className:"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200",children:[(0,a.jsx)(g,{className:"mr-3 h-5 w-5 text-gray-400"}),"Sign out"]})]})]}),(0,a.jsxs)("div",{className:"lg:pl-64 flex-1 flex flex-col min-h-screen",children:[(0,a.jsx)("div",{className:"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,a.jsx)("button",{onClick:()=>v(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,a.jsx)(y.A,{className:"h-6 w-6"})}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["Welcome back, ",r.name]})})]})}),(0,a.jsx)("main",{className:"flex-1 p-6 overflow-auto",children:t})]})]}):null}},7213:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7434:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(2115);let n=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...s,width:n,height:n,stroke:r,strokeWidth:o?24*Number(i)/Number(n):i,className:l("lucide",c),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),o=(e,t)=>{let r=(0,a.forwardRef)((r,s)=>{let{className:o,...c}=r;return(0,a.createElement)(i,{ref:s,iconNode:t,className:l("lucide-".concat(n(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),o),...c})});return r.displayName=n(e),r}}},e=>{e.O(0,[810,441,964,358],()=>e(e.s=2920)),_N_E=e.O()}]);