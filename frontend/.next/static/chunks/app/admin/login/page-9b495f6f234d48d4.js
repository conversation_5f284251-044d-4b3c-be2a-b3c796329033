(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[116],{1264:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},7273:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(5155),a=r(2115),l=r(5695),n=r(9946);let i=(0,n.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var o=r(1264);let d=(0,n.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var c=r(2657);function m(){let[e,t]=(0,a.useState)({email:"",password:""}),[r,n]=(0,a.useState)(!1),[m,u]=(0,a.useState)(!1),[h,x]=(0,a.useState)(""),p=(0,l.useRouter)(),g=async t=>{t.preventDefault(),u(!0),x("");try{let t=await fetch("".concat("http://localhost:5001/api","/auth/admin/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.json();t.ok?(localStorage.setItem("adminToken",r.token),localStorage.setItem("adminUser",JSON.stringify(r.user)),p.push("/admin/dashboard")):x(r.message||"Login failed")}catch(e){x("Network error. Please try again.")}finally{u(!1)}},y=r=>{t({...e,[r.target.name]:r.target.value})};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white rounded-xl shadow-lg p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(i,{className:"h-8 w-8 text-white"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"RevAdOps Admin"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Sign in to manage your website"})]}),h&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-600 text-sm",children:h})}),(0,s.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(o.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e.email,onChange:y,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",placeholder:"<EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(i,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"password",name:"password",type:r?"text":"password",required:!0,value:e.password,onChange:y,className:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",placeholder:"Enter your password"}),(0,s.jsx)("button",{type:"button",onClick:()=>n(!r),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:r?(0,s.jsx)(d,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,s.jsx)(c.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:m,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center",children:m?(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):"Sign In"})]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Default Admin Credentials:"}),(0,s.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Password:"})," RevAdOps"]})]})]})]})})}},9789:(e,t,r)=>{Promise.resolve().then(r.bind(r,7273))},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(2115);let a=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:m,...u}=e;return(0,s.createElement)("svg",{ref:t,...n,width:a,height:a,stroke:r,strokeWidth:o?24*Number(i)/Number(a):i,className:l("lucide",d),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let r=(0,s.forwardRef)((r,n)=>{let{className:o,...d}=r;return(0,s.createElement)(i,{ref:n,iconNode:t,className:l("lucide-".concat(a(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),o),...d})});return r.displayName=a(e),r}}},e=>{e.O(0,[441,964,358],()=>e(e.s=9789)),_N_E=e.O()}]);