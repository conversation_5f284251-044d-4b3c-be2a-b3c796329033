[{"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/analytics/page.tsx": "1", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/dashboard/page.tsx": "2", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/homepage/page.tsx": "3", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/layout.tsx": "4", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/leads/page.tsx": "5", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/login/page.tsx": "6", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/media/page.tsx": "7", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/services/page.tsx": "8", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/settings/page.tsx": "9", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/blog/page.tsx": "10", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/consultation/page.tsx": "11", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/contact/page.tsx": "12", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/layout.tsx": "13", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/page.tsx": "14", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/services/page.tsx": "15", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/BlogSection.tsx": "16", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ClientsSection.tsx": "17", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/CoreServicesSection.tsx": "18", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DragDropUpload.tsx": "19", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogCTASection.tsx": "20", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogCategoriesSection.tsx": "21", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogHeroSection.tsx": "22", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogListSection.tsx": "23", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationBenefitsSection.tsx": "24", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationFormSection.tsx": "25", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationHeroSection.tsx": "26", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactFormSection.tsx": "27", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactHeroSection.tsx": "28", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactInfoSection.tsx": "29", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactMapSection.tsx": "30", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicExpertiseSection.tsx": "31", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicFinalCTASection.tsx": "32", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHeroSection.tsx": "33", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHowItWorksSection.tsx": "34", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicPartnersSection.tsx": "35", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesCTASection.tsx": "36", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesFeaturesSection.tsx": "37", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesHeroSection.tsx": "38", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesListSection.tsx": "39", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesProcessSection.tsx": "40", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicTestimonialsSection.tsx": "41", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhatWeDoSection.tsx": "42", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhyChooseSection.tsx": "43", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ExpertiseSection.tsx": "44", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/FAQSection.tsx": "45", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/FinalCTASection.tsx": "46", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Footer.tsx": "47", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Header.tsx": "48", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/HeroSection.tsx": "49", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/HowItWorksSection.tsx": "50", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ServicesSection.tsx": "51", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/TestimonialsSection.tsx": "52", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/WhatWeDoSection.tsx": "53", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/WhyChooseSection.tsx": "54", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/contexts/ContentContext.tsx": "55", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/data/staticContent.ts": "56", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/lib/api.ts": "57", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/types/index.ts": "58"}, {"size": 10335, "mtime": 1755284627519, "results": "59", "hashOfConfig": "60"}, {"size": 7986, "mtime": 1755796081334, "results": "61", "hashOfConfig": "60"}, {"size": 19368, "mtime": 1755795749626, "results": "62", "hashOfConfig": "60"}, {"size": 6437, "mtime": 1755794320973, "results": "63", "hashOfConfig": "60"}, {"size": 11490, "mtime": 1755795243548, "results": "64", "hashOfConfig": "60"}, {"size": 5775, "mtime": 1755795261639, "results": "65", "hashOfConfig": "60"}, {"size": 11275, "mtime": 1755795931842, "results": "66", "hashOfConfig": "60"}, {"size": 19086, "mtime": 1755796058219, "results": "67", "hashOfConfig": "60"}, {"size": 13471, "mtime": 1755795311968, "results": "68", "hashOfConfig": "60"}, {"size": 2292, "mtime": 1755794577339, "results": "69", "hashOfConfig": "60"}, {"size": 2218, "mtime": 1755794591315, "results": "70", "hashOfConfig": "60"}, {"size": 2296, "mtime": 1755794605190, "results": "71", "hashOfConfig": "60"}, {"size": 1364, "mtime": 1755793591923, "results": "72", "hashOfConfig": "60"}, {"size": 2596, "mtime": 1755794547383, "results": "73", "hashOfConfig": "60"}, {"size": 2634, "mtime": 1755794563205, "results": "74", "hashOfConfig": "60"}, {"size": 3270, "mtime": 1755213065096, "results": "75", "hashOfConfig": "60"}, {"size": 2035, "mtime": 1755213044207, "results": "76", "hashOfConfig": "60"}, {"size": 5210, "mtime": 1755212454452, "results": "77", "hashOfConfig": "60"}, {"size": 4792, "mtime": 1755285902855, "results": "78", "hashOfConfig": "60"}, {"size": 7696, "mtime": 1755794895664, "results": "79", "hashOfConfig": "60"}, {"size": 5296, "mtime": 1755794689327, "results": "80", "hashOfConfig": "60"}, {"size": 3611, "mtime": 1755543391518, "results": "81", "hashOfConfig": "60"}, {"size": 9804, "mtime": 1755543391518, "results": "82", "hashOfConfig": "60"}, {"size": 8107, "mtime": 1755794803624, "results": "83", "hashOfConfig": "60"}, {"size": 21636, "mtime": 1755794910650, "results": "84", "hashOfConfig": "60"}, {"size": 6770, "mtime": 1755796114521, "results": "85", "hashOfConfig": "60"}, {"size": 15078, "mtime": 1755794939073, "results": "86", "hashOfConfig": "60"}, {"size": 3815, "mtime": 1755543391519, "results": "87", "hashOfConfig": "60"}, {"size": 8582, "mtime": 1755794818095, "results": "88", "hashOfConfig": "60"}, {"size": 8973, "mtime": 1755794954408, "results": "89", "hashOfConfig": "60"}, {"size": 2970, "mtime": 1755288911731, "results": "90", "hashOfConfig": "60"}, {"size": 2200, "mtime": 1755285082579, "results": "91", "hashOfConfig": "60"}, {"size": 3914, "mtime": 1755552728367, "results": "92", "hashOfConfig": "60"}, {"size": 3349, "mtime": 1755288844815, "results": "93", "hashOfConfig": "60"}, {"size": 2700, "mtime": 1755289038407, "results": "94", "hashOfConfig": "60"}, {"size": 5038, "mtime": 1755543391520, "results": "95", "hashOfConfig": "60"}, {"size": 4982, "mtime": 1755794968165, "results": "96", "hashOfConfig": "60"}, {"size": 4050, "mtime": 1755543391520, "results": "97", "hashOfConfig": "60"}, {"size": 5002, "mtime": 1755794845229, "results": "98", "hashOfConfig": "60"}, {"size": 4960, "mtime": 1755794860134, "results": "99", "hashOfConfig": "60"}, {"size": 4109, "mtime": 1755796130250, "results": "100", "hashOfConfig": "60"}, {"size": 5439, "mtime": 1755288686201, "results": "101", "hashOfConfig": "60"}, {"size": 3811, "mtime": 1755288773155, "results": "102", "hashOfConfig": "60"}, {"size": 6594, "mtime": 1755795711670, "results": "103", "hashOfConfig": "60"}, {"size": 5115, "mtime": 1755212546327, "results": "104", "hashOfConfig": "60"}, {"size": 5119, "mtime": 1755794995721, "results": "105", "hashOfConfig": "60"}, {"size": 2428, "mtime": 1755795919111, "results": "106", "hashOfConfig": "60"}, {"size": 2808, "mtime": 1755795677669, "results": "107", "hashOfConfig": "60"}, {"size": 2944, "mtime": 1755795695566, "results": "108", "hashOfConfig": "60"}, {"size": 4780, "mtime": 1755211728229, "results": "109", "hashOfConfig": "60"}, {"size": 5457, "mtime": 1755795021429, "results": "110", "hashOfConfig": "60"}, {"size": 5935, "mtime": 1755796145507, "results": "111", "hashOfConfig": "60"}, {"size": 2927, "mtime": 1755279009662, "results": "112", "hashOfConfig": "60"}, {"size": 7412, "mtime": 1755279056172, "results": "113", "hashOfConfig": "60"}, {"size": 2292, "mtime": 1755794536541, "results": "114", "hashOfConfig": "60"}, {"size": 12584, "mtime": 1755553683183, "results": "115", "hashOfConfig": "60"}, {"size": 2754, "mtime": 1755211193808, "results": "116", "hashOfConfig": "60"}, {"size": 2082, "mtime": 1755211207933, "results": "117", "hashOfConfig": "60"}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9b<PERSON><PERSON><PERSON>", {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/analytics/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/dashboard/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/homepage/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunase<PERSON>an/frontend/src/app/admin/leads/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/login/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/media/page.tsx", ["292", "293"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/services/page.tsx", ["294"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/settings/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/blog/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/consultation/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/contact/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/services/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/BlogSection.tsx", ["295"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ClientsSection.tsx", ["296"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/CoreServicesSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DragDropUpload.tsx", ["297"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogCTASection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogCategoriesSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogHeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogListSection.tsx", ["298", "299"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationBenefitsSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationFormSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationHeroSection.tsx", ["300", "301"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactFormSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactHeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactInfoSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactMapSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicExpertiseSection.tsx", ["302"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicFinalCTASection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHowItWorksSection.tsx", ["303"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicPartnersSection.tsx", ["304"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesCTASection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesFeaturesSection.tsx", ["305"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesHeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesListSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesProcessSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicTestimonialsSection.tsx", ["306", "307", "308", "309"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhatWeDoSection.tsx", ["310"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhyChooseSection.tsx", ["311"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ExpertiseSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/FAQSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/FinalCTASection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Footer.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Header.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/HeroSection.tsx", ["312"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/HowItWorksSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ServicesSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/TestimonialsSection.tsx", ["313", "314"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/WhatWeDoSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/WhyChooseSection.tsx", ["315"], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/contexts/ContentContext.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/data/staticContent.ts", [], [], "/Users/<USER>/Documents/augment-projects/Gunase<PERSON>an/frontend/src/lib/api.ts", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/types/index.ts", [], [], {"ruleId": "316", "severity": 1, "message": "317", "line": 230, "column": 23, "nodeType": "318", "endLine": 234, "endColumn": 25}, {"ruleId": "316", "severity": 1, "message": "317", "line": 258, "column": 23, "nodeType": "318", "endLine": 262, "endColumn": 25}, {"ruleId": "316", "severity": 1, "message": "317", "line": 329, "column": 29, "nodeType": "318", "endLine": 336, "endColumn": 31}, {"ruleId": "316", "severity": 1, "message": "317", "line": 47, "column": 17, "nodeType": "318", "endLine": 51, "endColumn": 19}, {"ruleId": "316", "severity": 1, "message": "317", "line": 37, "column": 15, "nodeType": "318", "endLine": 41, "endColumn": 17}, {"ruleId": "316", "severity": 1, "message": "317", "line": 104, "column": 11, "nodeType": "318", "endLine": 108, "endColumn": 13}, {"ruleId": "316", "severity": 1, "message": "317", "line": 171, "column": 19, "nodeType": "318", "endLine": 175, "endColumn": 21}, {"ruleId": "316", "severity": 1, "message": "317", "line": 187, "column": 17, "nodeType": "318", "endLine": 191, "endColumn": 19}, {"ruleId": "319", "severity": 2, "message": "320", "line": 113, "column": 21, "nodeType": "321", "messageId": "322", "suggestions": "323"}, {"ruleId": "319", "severity": 2, "message": "320", "line": 113, "column": 82, "nodeType": "321", "messageId": "322", "suggestions": "324"}, {"ruleId": "316", "severity": 1, "message": "317", "line": 70, "column": 19, "nodeType": "318", "endLine": 74, "endColumn": 21}, {"ruleId": "316", "severity": 1, "message": "317", "line": 84, "column": 19, "nodeType": "318", "endLine": 88, "endColumn": 21}, {"ruleId": "316", "severity": 1, "message": "317", "line": 67, "column": 15, "nodeType": "318", "endLine": 71, "endColumn": 17}, {"ruleId": "316", "severity": 1, "message": "317", "line": 101, "column": 15, "nodeType": "318", "endLine": 105, "endColumn": 17}, {"ruleId": "316", "severity": 1, "message": "317", "line": 85, "column": 19, "nodeType": "318", "endLine": 89, "endColumn": 21}, {"ruleId": "319", "severity": 2, "message": "320", "line": 93, "column": 17, "nodeType": "321", "messageId": "322", "suggestions": "325"}, {"ruleId": "319", "severity": 2, "message": "320", "line": 93, "column": 66, "nodeType": "321", "messageId": "322", "suggestions": "326"}, {"ruleId": "316", "severity": 1, "message": "317", "line": 97, "column": 19, "nodeType": "318", "endLine": 101, "endColumn": 21}, {"ruleId": "316", "severity": 1, "message": "317", "line": 97, "column": 21, "nodeType": "318", "endLine": 101, "endColumn": 23}, {"ruleId": "316", "severity": 1, "message": "317", "line": 90, "column": 21, "nodeType": "318", "endLine": 94, "endColumn": 23}, {"ruleId": "316", "severity": 1, "message": "317", "line": 32, "column": 9, "nodeType": "318", "endLine": 36, "endColumn": 11}, {"ruleId": "319", "severity": 2, "message": "320", "line": 75, "column": 15, "nodeType": "321", "messageId": "322", "suggestions": "327"}, {"ruleId": "319", "severity": 2, "message": "320", "line": 75, "column": 65, "nodeType": "321", "messageId": "322", "suggestions": "328"}, {"ruleId": "316", "severity": 1, "message": "317", "line": 124, "column": 21, "nodeType": "318", "endLine": 128, "endColumn": 23}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["329", "330", "331", "332"], ["333", "334", "335", "336"], ["337", "338", "339", "340"], ["341", "342", "343", "344"], ["345", "346", "347", "348"], ["349", "350", "351", "352"], {"messageId": "353", "data": "354", "fix": "355", "desc": "356"}, {"messageId": "353", "data": "357", "fix": "358", "desc": "359"}, {"messageId": "353", "data": "360", "fix": "361", "desc": "362"}, {"messageId": "353", "data": "363", "fix": "364", "desc": "365"}, {"messageId": "353", "data": "366", "fix": "367", "desc": "356"}, {"messageId": "353", "data": "368", "fix": "369", "desc": "359"}, {"messageId": "353", "data": "370", "fix": "371", "desc": "362"}, {"messageId": "353", "data": "372", "fix": "373", "desc": "365"}, {"messageId": "353", "data": "374", "fix": "375", "desc": "356"}, {"messageId": "353", "data": "376", "fix": "377", "desc": "359"}, {"messageId": "353", "data": "378", "fix": "379", "desc": "362"}, {"messageId": "353", "data": "380", "fix": "381", "desc": "365"}, {"messageId": "353", "data": "382", "fix": "383", "desc": "356"}, {"messageId": "353", "data": "384", "fix": "385", "desc": "359"}, {"messageId": "353", "data": "386", "fix": "387", "desc": "362"}, {"messageId": "353", "data": "388", "fix": "389", "desc": "365"}, {"messageId": "353", "data": "390", "fix": "391", "desc": "356"}, {"messageId": "353", "data": "392", "fix": "393", "desc": "359"}, {"messageId": "353", "data": "394", "fix": "395", "desc": "362"}, {"messageId": "353", "data": "396", "fix": "397", "desc": "365"}, {"messageId": "353", "data": "398", "fix": "399", "desc": "356"}, {"messageId": "353", "data": "400", "fix": "401", "desc": "359"}, {"messageId": "353", "data": "402", "fix": "403", "desc": "362"}, {"messageId": "353", "data": "404", "fix": "405", "desc": "365"}, "replaceWithAlt", {"alt": "406"}, {"range": "407", "text": "408"}, "Replace with `&quot;`.", {"alt": "409"}, {"range": "410", "text": "411"}, "Replace with `&ldquo;`.", {"alt": "412"}, {"range": "413", "text": "414"}, "Replace with `&#34;`.", {"alt": "415"}, {"range": "416", "text": "417"}, "Replace with `&rdquo;`.", {"alt": "406"}, {"range": "418", "text": "419"}, {"alt": "409"}, {"range": "420", "text": "421"}, {"alt": "412"}, {"range": "422", "text": "423"}, {"alt": "415"}, {"range": "424", "text": "425"}, {"alt": "406"}, {"range": "426", "text": "427"}, {"alt": "409"}, {"range": "428", "text": "429"}, {"alt": "412"}, {"range": "430", "text": "431"}, {"alt": "415"}, {"range": "432", "text": "433"}, {"alt": "406"}, {"range": "434", "text": "435"}, {"alt": "409"}, {"range": "436", "text": "437"}, {"alt": "412"}, {"range": "438", "text": "439"}, {"alt": "415"}, {"range": "440", "text": "441"}, {"alt": "406"}, {"range": "442", "text": "443"}, {"alt": "409"}, {"range": "444", "text": "445"}, {"alt": "412"}, {"range": "446", "text": "447"}, {"alt": "415"}, {"range": "448", "text": "449"}, {"alt": "406"}, {"range": "450", "text": "451"}, {"alt": "409"}, {"range": "452", "text": "453"}, {"alt": "412"}, {"range": "454", "text": "455"}, {"alt": "415"}, {"range": "456", "text": "457"}, "&quot;", [4803, 4852], "\n                    &quot;                    &ldquo;", "&ldquo;", [4803, 4852], "\n                    &ldquo;                    &ldquo;", "&#34;", [4803, 4852], "\n                    &#34;                    &ldquo;", "&rdquo;", [4803, 4852], "\n                    &rdquo;                    &ldquo;", [4878, 4905], "&rdquo;&quot;\n                  ", [4878, 4905], "&rdquo;&ldquo;\n                  ", [4878, 4905], "&rdquo;&#34;\n                  ", [4878, 4905], "&rdquo;&rdquo;\n                  ", [3352, 3393], "\n                &quot;                &ldquo;", [3352, 3393], "\n                &ldquo;                &ldquo;", [3352, 3393], "\n                &#34;                &ldquo;", [3352, 3393], "\n                &rdquo;                &ldquo;", [3411, 3434], "&rdquo;&quot;\n              ", [3411, 3434], "&rdquo;&ldquo;\n              ", [3411, 3434], "&rdquo;&#34;\n              ", [3411, 3434], "&rdquo;&rdquo;\n              ", [3090, 3127], "\n              &quot;              &ldquo;", [3090, 3127], "\n              &ldquo;              &ldquo;", [3090, 3127], "\n              &#34;              &ldquo;", [3090, 3127], "\n              &rdquo;              &ldquo;", [3148, 3169], "&rdquo;&quot;\n            ", [3148, 3169], "&rdquo;&ldquo;\n            ", [3148, 3169], "&rdquo;&#34;\n            ", [3148, 3169], "&rdquo;&rdquo;\n            "]