[{"/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/analytics/page.tsx": "1", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/dashboard/page.tsx": "2", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/homepage/page.tsx": "3", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/layout.tsx": "4", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/leads/page.tsx": "5", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/login/page.tsx": "6", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/media/page.tsx": "7", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/services/page.tsx": "8", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/settings/page.tsx": "9", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/blog/page.tsx": "10", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/consultation/page.tsx": "11", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/contact/page.tsx": "12", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/layout.tsx": "13", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/page.tsx": "14", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/services/page.tsx": "15", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/BlogSection.tsx": "16", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ClientsSection.tsx": "17", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/CoreServicesSection.tsx": "18", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DragDropUpload.tsx": "19", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogCTASection.tsx": "20", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogCategoriesSection.tsx": "21", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogHeroSection.tsx": "22", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogListSection.tsx": "23", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationBenefitsSection.tsx": "24", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationFormSection.tsx": "25", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationHeroSection.tsx": "26", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactFormSection.tsx": "27", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactHeroSection.tsx": "28", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactInfoSection.tsx": "29", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactMapSection.tsx": "30", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicExpertiseSection.tsx": "31", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicFinalCTASection.tsx": "32", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHeroSection.tsx": "33", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHowItWorksSection.tsx": "34", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicPartnersSection.tsx": "35", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesCTASection.tsx": "36", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesFeaturesSection.tsx": "37", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesHeroSection.tsx": "38", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesListSection.tsx": "39", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesProcessSection.tsx": "40", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicTestimonialsSection.tsx": "41", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhatWeDoSection.tsx": "42", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhyChooseSection.tsx": "43", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ExpertiseSection.tsx": "44", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/FAQSection.tsx": "45", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/FinalCTASection.tsx": "46", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Footer.tsx": "47", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Header.tsx": "48", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/HeroSection.tsx": "49", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/HowItWorksSection.tsx": "50", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ServicesSection.tsx": "51", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/TestimonialsSection.tsx": "52", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/WhatWeDoSection.tsx": "53", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/WhyChooseSection.tsx": "54", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/contexts/ContentContext.tsx": "55", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/data/staticContent.ts": "56", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/lib/api.ts": "57", "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/types/index.ts": "58"}, {"size": 10335, "mtime": 1755284627519, "results": "59", "hashOfConfig": "60"}, {"size": 8026, "mtime": 1755798506488, "results": "61", "hashOfConfig": "60"}, {"size": 19460, "mtime": 1755796798196, "results": "62", "hashOfConfig": "60"}, {"size": 6437, "mtime": 1755794320973, "results": "63", "hashOfConfig": "60"}, {"size": 11490, "mtime": 1755795243548, "results": "64", "hashOfConfig": "60"}, {"size": 5775, "mtime": 1755795261639, "results": "65", "hashOfConfig": "60"}, {"size": 11397, "mtime": 1755799005364, "results": "66", "hashOfConfig": "60"}, {"size": 19272, "mtime": 1755799039838, "results": "67", "hashOfConfig": "60"}, {"size": 13481, "mtime": 1755796894149, "results": "68", "hashOfConfig": "60"}, {"size": 2292, "mtime": 1755794577339, "results": "69", "hashOfConfig": "60"}, {"size": 2218, "mtime": 1755794591315, "results": "70", "hashOfConfig": "60"}, {"size": 2296, "mtime": 1755794605190, "results": "71", "hashOfConfig": "60"}, {"size": 1407, "mtime": 1755799088382, "results": "72", "hashOfConfig": "60"}, {"size": 2596, "mtime": 1755794547383, "results": "73", "hashOfConfig": "60"}, {"size": 2634, "mtime": 1755794563205, "results": "74", "hashOfConfig": "60"}, {"size": 3366, "mtime": 1755798575053, "results": "75", "hashOfConfig": "60"}, {"size": 2126, "mtime": 1755798604718, "results": "76", "hashOfConfig": "60"}, {"size": 5210, "mtime": 1755212454452, "results": "77", "hashOfConfig": "60"}, {"size": 4875, "mtime": 1755798670308, "results": "78", "hashOfConfig": "60"}, {"size": 7696, "mtime": 1755794895664, "results": "79", "hashOfConfig": "60"}, {"size": 5296, "mtime": 1755794689327, "results": "80", "hashOfConfig": "60"}, {"size": 3611, "mtime": 1755543391518, "results": "81", "hashOfConfig": "60"}, {"size": 9860, "mtime": 1755798716001, "results": "82", "hashOfConfig": "60"}, {"size": 8107, "mtime": 1755794803624, "results": "83", "hashOfConfig": "60"}, {"size": 21636, "mtime": 1755794910650, "results": "84", "hashOfConfig": "60"}, {"size": 6748, "mtime": 1755796643142, "results": "85", "hashOfConfig": "60"}, {"size": 15078, "mtime": 1755794939073, "results": "86", "hashOfConfig": "60"}, {"size": 3815, "mtime": 1755543391519, "results": "87", "hashOfConfig": "60"}, {"size": 8582, "mtime": 1755794818095, "results": "88", "hashOfConfig": "60"}, {"size": 8973, "mtime": 1755794954408, "results": "89", "hashOfConfig": "60"}, {"size": 3068, "mtime": 1755798751578, "results": "90", "hashOfConfig": "60"}, {"size": 2200, "mtime": 1755285082579, "results": "91", "hashOfConfig": "60"}, {"size": 3914, "mtime": 1755552728367, "results": "92", "hashOfConfig": "60"}, {"size": 3447, "mtime": 1755798801948, "results": "93", "hashOfConfig": "60"}, {"size": 2852, "mtime": 1755799120668, "results": "94", "hashOfConfig": "60"}, {"size": 5038, "mtime": 1755543391520, "results": "95", "hashOfConfig": "60"}, {"size": 5074, "mtime": 1755798858413, "results": "96", "hashOfConfig": "60"}, {"size": 4050, "mtime": 1755543391520, "results": "97", "hashOfConfig": "60"}, {"size": 5002, "mtime": 1755794845229, "results": "98", "hashOfConfig": "60"}, {"size": 4960, "mtime": 1755794860134, "results": "99", "hashOfConfig": "60"}, {"size": 4255, "mtime": 1755798895772, "results": "100", "hashOfConfig": "60"}, {"size": 5541, "mtime": 1755798923521, "results": "101", "hashOfConfig": "60"}, {"size": 3913, "mtime": 1755798956814, "results": "102", "hashOfConfig": "60"}, {"size": 6594, "mtime": 1755795711670, "results": "103", "hashOfConfig": "60"}, {"size": 5115, "mtime": 1755212546327, "results": "104", "hashOfConfig": "60"}, {"size": 5119, "mtime": 1755794995721, "results": "105", "hashOfConfig": "60"}, {"size": 2428, "mtime": 1755795919111, "results": "106", "hashOfConfig": "60"}, {"size": 2808, "mtime": 1755795677669, "results": "107", "hashOfConfig": "60"}, {"size": 2998, "mtime": 1755798545421, "results": "108", "hashOfConfig": "60"}, {"size": 4780, "mtime": 1755211728229, "results": "109", "hashOfConfig": "60"}, {"size": 5457, "mtime": 1755795021429, "results": "110", "hashOfConfig": "60"}, {"size": 5919, "mtime": 1755796662474, "results": "111", "hashOfConfig": "60"}, {"size": 2927, "mtime": 1755279009662, "results": "112", "hashOfConfig": "60"}, {"size": 7513, "mtime": 1755798637541, "results": "113", "hashOfConfig": "60"}, {"size": 2292, "mtime": 1755794536541, "results": "114", "hashOfConfig": "60"}, {"size": 12584, "mtime": 1755553683183, "results": "115", "hashOfConfig": "60"}, {"size": 2754, "mtime": 1755211193808, "results": "116", "hashOfConfig": "60"}, {"size": 2082, "mtime": 1755211207933, "results": "117", "hashOfConfig": "60"}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9b<PERSON><PERSON><PERSON>", {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/analytics/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/dashboard/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/homepage/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunase<PERSON>an/frontend/src/app/admin/leads/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/login/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/media/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/services/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/settings/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/blog/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/consultation/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/contact/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/services/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/BlogSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ClientsSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/CoreServicesSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DragDropUpload.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogCTASection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogCategoriesSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogHeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicBlogListSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationBenefitsSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationFormSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicConsultationHeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactFormSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactHeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactInfoSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicContactMapSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicExpertiseSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicFinalCTASection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicHowItWorksSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicPartnersSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesCTASection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesFeaturesSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesHeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesListSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicServicesProcessSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicTestimonialsSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhatWeDoSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/DynamicWhyChooseSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ExpertiseSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/FAQSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/FinalCTASection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Footer.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/Header.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/HeroSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/HowItWorksSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/ServicesSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/TestimonialsSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/WhatWeDoSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/components/WhyChooseSection.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/contexts/ContentContext.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/data/staticContent.ts", [], [], "/Users/<USER>/Documents/augment-projects/Gunase<PERSON>an/frontend/src/lib/api.ts", [], [], "/Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/types/index.ts", [], []]